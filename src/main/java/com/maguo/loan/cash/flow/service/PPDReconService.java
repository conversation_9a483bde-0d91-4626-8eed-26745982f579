package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.PpdRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.ppd.vo.LoanInfoVo;
import com.maguo.loan.cash.flow.entity.ppd.vo.RepaymentInfoVo;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PpdRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.service.PPDReconService
 * @作者 Mr.sandman
 * @时间 2025/06/23 09:50
 */
@Service
public class PPDReconService {

    private static final Logger logger = LoggerFactory.getLogger(PPDReconService.class);

    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private PpdRepayApplyRecordRepository ppdRepayApplyRecordRepository;

    // 获取放款对账信息
    public List<LoanInfoVo> getLoanDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel) {
        List<LoanInfoVo> loanInfoVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<Loan> loanList = loanRepository.findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(yesterdayStart, yesterdayEnd, ProcessState.SUCCEED, flowChannel, bankChannel);
        if (loanList != null && loanList.size() > 0) {
            for (Loan loan : loanList) {
                LoanInfoVo loanInfoVo = new LoanInfoVo();
                loanInfoVo.setLoanReqNo(loan.getOuterLoanId());
                ApplyChannel applyChannelByCode = ApplyChannel.getApplyChannelByCode(loan.getApplyChannel());
                loanInfoVo.setSourceCode(applyChannelByCode == null ? null : applyChannelByCode.name());
                loanInfoVo.setCashDate(loan.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                loanInfoVo.setLoanAmt(loan.getAmount());
                loanInfoVoList.add(loanInfoVo);
            }
        }
        return loanInfoVoList;
    }


    // 获取还款对账信息
    public List<RepaymentInfoVo> getRepayDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel) {
        List<RepaymentInfoVo> repaymentInfoVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay();
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX);
        List<CustomRepayRecord> repayRecordList = customRepayRecordRepository.findByRepaidDateBetweenAndRepayState(yesterdayStart, yesterdayEnd, ProcessState.SUCCEED);
        if (repayRecordList != null && repayRecordList.size() > 0) {
            for (CustomRepayRecord repayRecord : repayRecordList) {
                Loan loan = loanRepository.findByIdAndFlowChannelAndBankChannel(repayRecord.getLoanId(), flowChannel, bankChannel).orElse(null);
                if ( loan == null ) {
                    continue;
                }
                RepaymentInfoVo repaymentInfoVo = new RepaymentInfoVo();
                    repaymentInfoVo.setLoanReqNo(loan.getOuterLoanId());
                    ApplyChannel applyChannelByCode = ApplyChannel.getApplyChannelByCode(loan.getApplyChannel());
                    repaymentInfoVo.setSourceCode(applyChannelByCode == null ? null : applyChannelByCode.name());
                    repaymentInfoVo.setRepayNo(repayRecord.getOuterRepayNo());
                    if (repayRecord.getRepayMode().equals(RepayMode.ONLINE)) {
                        repaymentInfoVo.setRepayMode("01");
                    } else if (repayRecord.getRepayMode().equals(RepayMode.OFFLINE)) {
                        repaymentInfoVo.setRepayMode("02");
                    }
                    PpdRepayApplyRecord ppdRepayApplyRecord = ppdRepayApplyRecordRepository.findByOutRepayId(repayRecord.getOuterRepayNo()).orElseThrow();
                    repaymentInfoVo.setRepayType(ppdRepayApplyRecord.getRepayType());
                    repaymentInfoVo.setRepayDate(repayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    repaymentInfoVo.setRepayTerm(repayRecord.getPeriod());
                    repaymentInfoVo.setRepayAmount(repayRecord.getTotalAmt());
                    repaymentInfoVo.setRepayPrincipal(repayRecord.getPrincipalAmt());
                    repaymentInfoVo.setRepayInterest(repayRecord.getInterestAmt());
                    repaymentInfoVo.setRepayOverdue(repayRecord.getPenaltyAmt());
                    repaymentInfoVo.setRepayPoundage(repayRecord.getConsultFee());
                    repaymentInfoVo.setRepayLateFee(repayRecord.getBreachAmt());
                    repaymentInfoVoList.add(repaymentInfoVo);

            }
        }
        return repaymentInfoVoList;
    }

    // 处理 null 的字段
    public static String safe(String val) {
        return val == null ? "" : val;
    }

    public static String safe(BigDecimal val) {
        return val == null ? "" : val.toPlainString();
    }

}
