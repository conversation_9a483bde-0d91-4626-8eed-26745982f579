package com.maguo.loan.cash.flow.entrance.ppd.dto.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 18:28
 **/
public class CreditApplyRequest {
    /**
     * 授信请求流水号
     */
    @NotBlank(message = "授信请求流水号不能为空")
    private String loanReqNo;

    /**
     * 授信申请时间，格式 yyyyMMddHHmmss
     */
    @NotBlank(message = "授信申请时间不能为空")
    @Pattern(
        regexp = "^(?:(?!0000)[0-9]{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])([01][0-9]|2[0-3])[0-5][0-9][0-5][0-9])$",
        message = "时间格式必须为yyyyMMddHHmmss且有效"
    )
    private String creditApplyTime;

    /**
     * 请求方代码
     */
    @NotBlank(message = "请求方代码不能为空")
//    @Pattern(
//        regexp =  "^(CJCYDL_PPD2|CJCYDL_PPD3)$",
//        message = "请求方代码必须是CJCYDL_PPD2或CJCYDL_PPD3"
//    )
    private String sourceCode;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String custName;

    /**
     * 证件类型，1: 身份证（暂时只支持该选项）
     */
    @NotBlank(message = "证件类型不能为空")
    private String idType;

    /**
     * 证号证件
     */
    @NotBlank(message = "证号证件不能为空")
    private String idNo;

    /**
     * 性别，1: 男，2: 女
     */
    @NotBlank(message = "性别不能为空")
    private String sex;

    /**
     * 年龄，按身份证计算
     */
    @NotNull(message = "年龄不能为空")
    private Integer age;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobileNo;

    /**
     * 婚姻状况，1: 未婚，2: 已婚，3: 其他
     */
    @NotBlank(message = "婚姻状况不能为空")
    private String marriage;

    /**
     * 学历等级
     */
    @NotBlank(message = "学历等级不能为空")
    private String eduLevel;

    /**
     * 最高学位
     */
    @NotBlank(message = "最高学位不能为空")
    private String highestDegree;

    /**
     * 现居地址
     */
    @NotBlank(message = "现居地址不能为空")
    private String address;

    /**
     * 职业
     */
    @NotBlank(message = "职业不能为空")
    private String occupation;

    /**
     * 借款金额，单位: 元
     */
    @Digits( integer = 19, fraction = 0, message = "金额必须是整数")
    private BigDecimal loanAmt;

    /**
     * 期数
     */
    private Integer loanTerm;

    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 期限类型
     */
    private String periodType;

    /**
     * 还款方式
     */
    private String refundMethod;

    /**
     * 扣款日类型
     */
    private String chargeDateType;

    /**
     * 贷款类型
     */
    private String loanType;

    /**
     * 放款银行代码
     */
    private String bankCode;

    /**
     * 放款卡号
     */
    private String bankAcct;

    /**
     * 放款银行卡账户名
     */
    private String acctName;

    /**
     * 放款卡持卡人预留手机号
     */
    private String bankMobile;

    /**
     * 还款银行代码
     */
    private String repayBankCode;

    /**
     * 还款卡号
     */
    private String repayBankAcct;

    /**
     * 还款银行卡账户名
     */
    private String repayAcctName;

    /**
     * 还款卡持卡人预留手机号
     */
    private String repayBankMobile;

    /**
     * 户籍地址（身份证 OCR）
     */
    @NotBlank(message = "户籍地址不能为空")
    private String addressOcr;

    /**
     * 身份证有效期起始日期
     */
    @NotBlank(message = "身份证有效期起始日期不能为空")
    private String idBeginDate;

    /**
     * 身份证有效期结束日期，长期有效传 9999-12-31
     */
    @NotBlank(message = "身份证有效期结束日期不能为空")
    private String idExpiryDate;

    /**
     * 签发机关
     */
    @NotBlank(message = "签发机关不能为空")
    private String certificationUnit;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空")
    private String nation;

    /**
     * 国籍
     */
    @NotBlank(message = "国籍不能为空")
    private String nationality;

    /**
     * 联系人列表
     */
    @Valid
    @NotNull(message = "联系人列表不能为空")
    private List<ContractInfo> contactList;
    /**
     * 渠道标识
     */
    private String accessType;

    @NotBlank(message = "人脸识别分数不能为空")
    private String faceRecoScore;

    @NotBlank(message = "人脸识别供不能为空")
    private String faceRecoChannel;

    public @NotBlank(message = "授信请求流水号不能为空") String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(@NotBlank(message = "授信请求流水号不能为空") String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getCreditApplyTime() {
        return creditApplyTime;
    }

    public void setCreditApplyTime(String creditApplyTime) {
        this.creditApplyTime = creditApplyTime;
    }

    public @NotBlank(message = "请求方代码不能为空") String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(@NotBlank(message = "请求方代码不能为空") String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public @NotBlank(message = "客户名称不能为空") String getCustName() {
        return custName;
    }

    public void setCustName(@NotBlank(message = "客户名称不能为空") String custName) {
        this.custName = custName;
    }

    public @NotBlank(message = "证件类型不能为空") String getIdType() {
        return idType;
    }

    public void setIdType(@NotBlank(message = "证件类型不能为空") String idType) {
        this.idType = idType;
    }

    public @NotBlank(message = "证号证件不能为空") String getIdNo() {
        return idNo;
    }

    public void setIdNo(@NotBlank(message = "证号证件不能为空") String idNo) {
        this.idNo = idNo;
    }

    public @NotBlank(message = "性别不能为空") String getSex() {
        return sex;
    }

    public void setSex(@NotBlank(message = "性别不能为空") String sex) {
        this.sex = sex;
    }

    public @NotNull(message = "年龄不能为空") Integer getAge() {
        return age;
    }

    public void setAge(@NotNull(message = "年龄不能为空") Integer age) {
        this.age = age;
    }

    public @NotBlank(message = "手机号不能为空") String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(@NotBlank(message = "手机号不能为空") String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public @NotBlank(message = "婚姻状况不能为空") String getMarriage() {
        return marriage;
    }

    public void setMarriage(@NotBlank(message = "婚姻状况不能为空") String marriage) {
        this.marriage = marriage;
    }

    public @NotBlank(message = "学历等级不能为空") String getEduLevel() {
        return eduLevel;
    }

    public void setEduLevel(@NotBlank(message = "学历等级不能为空") String eduLevel) {
        this.eduLevel = eduLevel;
    }

    public @NotBlank(message = "最高学位不能为空") String getHighestDegree() {
        return highestDegree;
    }

    public void setHighestDegree(@NotBlank(message = "最高学位不能为空") String highestDegree) {
        this.highestDegree = highestDegree;
    }

    public @NotBlank(message = "现居地址不能为空") String getAddress() {
        return address;
    }

    public void setAddress(@NotBlank(message = "现居地址不能为空") String address) {
        this.address = address;
    }

    public @NotBlank(message = "职业不能为空") String getOccupation() {
        return occupation;
    }

    public void setOccupation(@NotBlank(message = "职业不能为空") String occupation) {
        this.occupation = occupation;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public String getRefundMethod() {
        return refundMethod;
    }

    public void setRefundMethod(String refundMethod) {
        this.refundMethod = refundMethod;
    }

    public String getChargeDateType() {
        return chargeDateType;
    }

    public void setChargeDateType(String chargeDateType) {
        this.chargeDateType = chargeDateType;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayBankAcct() {
        return repayBankAcct;
    }

    public void setRepayBankAcct(String repayBankAcct) {
        this.repayBankAcct = repayBankAcct;
    }

    public String getRepayAcctName() {
        return repayAcctName;
    }

    public void setRepayAcctName(String repayAcctName) {
        this.repayAcctName = repayAcctName;
    }

    public String getRepayBankMobile() {
        return repayBankMobile;
    }

    public void setRepayBankMobile(String repayBankMobile) {
        this.repayBankMobile = repayBankMobile;
    }

    public @NotBlank(message = "户籍地址不能为空") String getAddressOcr() {
        return addressOcr;
    }

    public void setAddressOcr(@NotBlank(message = "户籍地址不能为空") String addressOcr) {
        this.addressOcr = addressOcr;
    }

    public @NotBlank(message = "身份证有效期起始日期不能为空") String getIdBeginDate() {
        return idBeginDate;
    }

    public void setIdBeginDate(@NotBlank(message = "身份证有效期起始日期不能为空") String idBeginDate) {
        this.idBeginDate = idBeginDate;
    }

    public @NotBlank(message = "身份证有效期结束日期不能为空") String getIdExpiryDate() {
        return idExpiryDate;
    }

    public void setIdExpiryDate(@NotBlank(message = "身份证有效期结束日期不能为空") String idExpiryDate) {
        this.idExpiryDate = idExpiryDate;
    }

    public @NotBlank(message = "签发机关不能为空") String getCertificationUnit() {
        return certificationUnit;
    }

    public void setCertificationUnit(@NotBlank(message = "签发机关不能为空") String certificationUnit) {
        this.certificationUnit = certificationUnit;
    }

    public @NotBlank(message = "民族不能为空") String getNation() {
        return nation;
    }

    public void setNation(@NotBlank(message = "民族不能为空") String nation) {
        this.nation = nation;
    }

    public @NotBlank(message = "国籍不能为空") String getNationality() {
        return nationality;
    }

    public void setNationality(@NotBlank(message = "国籍不能为空") String nationality) {
        this.nationality = nationality;
    }

    public @Valid @NotNull(message = "联系人列表不能为空") List<ContractInfo> getContactList() {
        return contactList;
    }

    public void setContactList(@Valid @NotNull(message = "联系人列表不能为空") List<ContractInfo> contactList) {
        this.contactList = contactList;
    }

    public @NotBlank(message = "人脸识别分数不能为空") String getFaceRecoScore() {
        return faceRecoScore;
    }

    public void setFaceRecoScore(@NotBlank(message = "人脸识别分数不能为空") String faceRecoScore) {
        this.faceRecoScore = faceRecoScore;
    }

    public @NotBlank(message = "人脸识别供不能为空") String getFaceRecoChannel() {
        return faceRecoChannel;
    }

    public void setFaceRecoChannel(@NotBlank(message = "人脸识别供不能为空") String faceRecoChannel) {
        this.faceRecoChannel = faceRecoChannel;
    }

    public String getAccessType() {
        return accessType;
    }

    public void setAccessType(String accessType) {
        this.accessType = accessType;
    }
}
