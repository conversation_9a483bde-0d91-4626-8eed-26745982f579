package com.maguo.loan.cash.flow.repository.ppd;

import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR> gale
 * @Description
 * @Date 2024/2/19 16:26
 */
public interface PpdCreditApplyRecordRepository extends JpaRepository<PpdCreditApplyRecord, String> {

    PpdCreditApplyRecord findByLoanReqNo(String loanReqNo);

    PpdCreditApplyRecord findTopByIdNoOrderByCreatedTimeDesc(String idNo);

}
