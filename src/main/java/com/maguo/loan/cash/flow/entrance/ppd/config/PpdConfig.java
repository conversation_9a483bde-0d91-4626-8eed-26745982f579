package com.maguo.loan.cash.flow.entrance.ppd.config;

import com.jinghang.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 拍拍贷加解密配置
 */
@Configuration
public class PpdConfig {

    /**
     * 客户端公钥
     */
    @Value("${ppd.client.publicKey}")
    private String clientPublicKey;

    /**
     * 服务端私钥
     */
    @Value("${ppd.server.privateKey}")
    private String serverPrivateKey;

    /**
     * 拍拍请求地址
     */
    @Value("${ppd.request.url}")
    private String ppdRequestUrl;


    @Value("${ppd.sftp.zzln.user}")
    private String sftpUser;
    @Value("${ppd.sftp.zzln.password}")
    private String sftpPassword;
    @Value("${ppd.sftp.zzln.host}")
    private String sftpHost;
    @Value("${ppd.sftp.zzln.port}")
    private Integer sftpPort;
    @Value("${ppd.sftp.dir.down}")
    private String sftpDownloadPath;
    @Value("${ppd.sftp.credit.apply.down}")
    private String sftpCreditApplyDownloadPath;

    @Value("${ppd.sftp.dir.upload}")
    private String sftpUploadPath;

    /**
     * 是否跳过验签
     */
    @Value("${ppd.skipSignVerify:false}")
    private Boolean skipSignVerify;

    @Value("${ppd.sys.time}")
    private String rlSysTime;

    @Value("${ppd.sysTime.mock}")
    private String rlSysTimeMock;

    @Value("${ppd.white.list}")
    private String whiteList;

    @Value("${ppd.white.switch}")
    private Boolean whiteSwitch;

    @Value("${ppd.sftp.dir.loan.path}")
    private String sftpLoanPath;

    @Value("${ppd.sftp.dir.repay.path}")
    private String sftpRepayPath;
    /**
     * ppd-huxiao的sftp配置
     */
    @Value("${ppd.hx.sftp.zzln.host}")
    private String sftpHxHost;
    @Value("${ppd.hx.sftp.zzln.port}")
    private Integer sftpHxPort;
    //ppd-huxiao
    @Value("${ppd.hx.sftp.user}")
    private String sftpHxUser;
    //ppd-huxiao
    @Value("${ppd.hx.sftp.password}")
    private String sftpHxPassword;
    //ppd-huxiao
    @Value("${ppd.hx.sftp.credit.apply.down}")
    private String sftpHxCreditApplyDownloadPath;
    //ppd-huxiao
    @Value("${ppd.hx.sftp.dir.down}")
    private String sftpHxDownloadPath;

    public String getSftpLoanPath() {
        return sftpLoanPath;
    }

    public void setSftpLoanPath( String sftpLoanPath ) {
        this.sftpLoanPath = sftpLoanPath;
    }

    public String getSftpRepayPath() {
        return sftpRepayPath;
    }

    public void setSftpRepayPath( String sftpRepayPath ) {
        this.sftpRepayPath = sftpRepayPath;
    }

    public String getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(String whiteList) {
        this.whiteList = whiteList;
    }

    public Boolean getWhiteSwitch() {
        return whiteSwitch;
    }

    public void setWhiteSwitch(Boolean whiteSwitch) {
        this.whiteSwitch = whiteSwitch;
    }

    public LocalDateTime isMockTime(LocalDateTime time) {
        return "true".equals(rlSysTimeMock) ? LocalDateTime.parse(rlSysTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : time;
    }

    public String getRlSysTime() {
        return rlSysTime;
    }

    public String getRlSysTimeMock() {
        return rlSysTimeMock;
    }

    public String getSftpUser() {
        return sftpUser;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getSftpDownloadPath() {
        return sftpDownloadPath;
    }

    public String getSftpUploadPath() {
        return sftpUploadPath;
    }

    public Boolean getSkipSignVerify() {
        return skipSignVerify;
    }

    public String getClientPublicKey() {
        return clientPublicKey;
    }

    public String getServerPrivateKey() {
        return serverPrivateKey;
    }


    public String getPpdRequestUrl() {
        return ppdRequestUrl;
    }

    public String getSftpCreditApplyDownloadPath() {
        return sftpCreditApplyDownloadPath;
    }

    public String getSftpHxUser() {
        return sftpHxUser;
    }

    public void setSftpHxUser(String sftpHxUser) {
        this.sftpHxUser = sftpHxUser;
    }

    public String getSftpHxCreditApplyDownloadPath() {
        return sftpHxCreditApplyDownloadPath;
    }

    public void setSftpHxCreditApplyDownloadPath(String sftpHxCreditApplyDownloadPath) {
        this.sftpHxCreditApplyDownloadPath = sftpHxCreditApplyDownloadPath;
    }

    public String getSftpHxPassword() {
        return sftpHxPassword;
    }

    public void setSftpHxPassword(String sftpHxPassword) {
        this.sftpHxPassword = sftpHxPassword;
    }

    public String getSftpHxDownloadPath() {
        return sftpHxDownloadPath;
    }

    public void setSftpHxDownloadPath(String sftpHxDownloadPath) {
        this.sftpHxDownloadPath = sftpHxDownloadPath;
    }

    public Integer getSftpHxPort() {
        return sftpHxPort;
    }

    public void setSftpHxPort(Integer sftpHxPort) {
        this.sftpHxPort = sftpHxPort;
    }

    public String getSftpHxHost() {
        return sftpHxHost;
    }

    public void setSftpHxHost(String sftpHxHost) {
        this.sftpHxHost = sftpHxHost;
    }

}
