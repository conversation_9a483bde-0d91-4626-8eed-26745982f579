package com.maguo.loan.cash.flow.entrance.ppd.service;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.BindCardRelation;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.LvxinApplyRecord;
import com.maguo.loan.cash.flow.entity.LvxinBankList;
import com.maguo.loan.cash.flow.entity.LvxinRebindRecord;
import com.maguo.loan.cash.flow.entity.LvxinRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdLoanApplyRecord;
import com.maguo.loan.cash.flow.entrance.common.constant.LvxinSysTimeMockService;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.entrance.lvxin.convert.LvxinConvert;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardApplyRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardApplyResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardConfirmRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardConfirmResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.LvxinBankInfo;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.QueryBankListRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.QueryBankListResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.contract.ContractRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.contract.ContractResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LoanTrialRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LoanTrialResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlan;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinGetRightsUrlRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinGetRightsUrlResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayTrialResponseV2;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayVerifyRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayVerifyResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.QuotaQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.QuotaQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UploadRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UploadResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinQuotaStatus;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinRepayStatus;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.entrance.ppd.convert.PpdConvert;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.ContractInfo;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.LoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.RepayPlanQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.CreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.LoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.LoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.LoanRevokeFundApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.RepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdErrorCode;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdOrderStatus;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdRepayStatus;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.impl.AlipayCardBinService;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.BindCardRelationRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanFailFollowRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.LvxinApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.LvxinRebindRecordRepository;
import com.maguo.loan.cash.flow.repository.LvxinRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderBindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRegisterRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdCreditApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdLoanApplyRecordRepository;
import com.maguo.loan.cash.flow.service.CheckService;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.LoanLimitCheckService;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.service.UserFileService;
import com.maguo.loan.cash.flow.service.UserService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.agreement.AgreementShowService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.bound.PlatformCardService;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.CommonRepayPlanCalc;
import com.maguo.loan.cash.flow.util.NumConstants;
import com.maguo.loan.cash.flow.util.RepayPlanItem;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 绿信 业务服务类
 * @Date 2024/5/15 14:40
 * @Version v1.0
 **/
@Slf4j
@Service
public class PpdService {
    private static final Logger logger = LoggerFactory.getLogger(PpdService.class);

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    public static final long SEVEN = 7L;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CheckService checkService;
    @Autowired
    private FileService fileService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private AgreementService agreementService;
    @Autowired
    private MqService mqService;
    @Autowired
    private LockService lockService;
    @Autowired
    private AgreementShowService agreementShowService;
    @Autowired
    private PlatformCardService platformCardService;
    @Autowired
    private CapitalCardService capitalCardService;
    @Autowired
    private TrialService trialService;
    @Autowired
    private RepayService repayService;
    @Autowired
    private LoanService loanService;
    @Autowired
    private OrderBindCardRecordRepository orderBindCardRecordRepository;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private UserRegisterRepository userRegisterRepository;
    @Autowired
    private LvxinApplyRecordRepository lvxinApplyRecordRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private LvxinRebindRecordRepository lvxinRebindRecordRepository;
    @Autowired
    private LvxinRepayApplyRecordRepository lvxinRepayApplyRecordRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private RepayPlanService repayPlanService;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private BindCardRelationRepository relationRepository;
    @Autowired
    private AlipayCardBinService alipayCardBinService;
    @Autowired
    LvxinSysTimeMockService lvxinSysTimeMockService;

    @Autowired
    private PpdLoanApplyRecordRepository loanApplyRecordRepository;

    @Autowired
    private PpdCreditApplyRecordRepository ppdCreditApplyRecordRepository;

    @Autowired
    private LoanLimitCheckService loanLimitCheckService;

    private final Integer thirty = 30;
    private static final String CCB_ABBR = "CCB";

    public static final Duration DEFAULT_LOCK_RELEASE_TIME = Duration.ofSeconds(8);

    public static final String APPROVAL_APPLY = "_approval_apply:";

    public static final String LOAN_APPLY = "_loan_apply:";

    @Autowired
    private LoanFailFollowRepository loanFailFollowRepository;

    @Autowired
    private PpdConfig ppdConfig;

    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private PpdLoanApplyRecordRepository ppdLoanApplyRecordRepository;
    @Autowired
    private UserOcrRepository userOcrRepository;
    @Autowired
    private UserFaceRepository userFaceRepository;
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    /**
     * 获取协议
     */
    public ContractResponse getContract(ContractRequest contractRequest) {
        ContractResponse response = new ContractResponse();
        List<AgreementShow> agreements = agreementShowService.getAgreements(FlowChannel.LVXIN, contractRequest.getType());
        response.setContracts(LvxinConvert.INSTANCE.toContractResponseList(agreements));
        return response;
    }

    /**
     * 获取支持的银行列表
     *
     * @param request
     * @return
     */
    public QueryBankListResponse supportedBankList(QueryBankListRequest request) {
        List<LvxinBankList> list = null;
        String orderNo = request.getPartnerUserId();
        if (StringUtils.isNotBlank(orderNo)) {
            //查询预订单
            String riskId = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.LVXIN).map(PreOrder::getRiskId).orElse(null);
            if (StringUtils.isNotBlank(riskId)) {
                Order order = orderRepository.findByRiskId(riskId);
                if (Objects.nonNull(order) && RightsLevel.NONE != order.getApproveRights()) {
                    //建设银行不支持扣权益，如果为权益订单，则移除建设银行CCB
                    list = list.stream().filter(s -> !StringUtils.equals(CCB_ABBR, s.getBankCode())).collect(Collectors.toList());
                }
            }
        }

        List<LvxinBankInfo> bankList = LvxinConvert.INSTANCE.toBankList(list);
        QueryBankListResponse response = new QueryBankListResponse();
        response.setBankList(bankList);
        return response;
    }

    /**
     * 授信进件
     */
    public CreditApplyResponse approval(CreditApplyRequest creditApplyRequest) {
        CreditApplyResponse creditApplyResponse = new CreditApplyResponse();
        String orderNo = creditApplyRequest.getLoanReqNo();
        Locker lock = lockService.getLock(FlowChannel.PPCJDL.name() + APPROVAL_APPLY + orderNo + creditApplyRequest.getSourceCode());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, DEFAULT_LOCK_RELEASE_TIME);
            if (!locked) {
                creditApplyResponse.setStatus("01");
                creditApplyResponse.setMsg("重复提交");
                return creditApplyResponse;
            }

            // 预检查文件是否上传完毕
            checkFtpFileReady(creditApplyRequest);

            //查询预订单，是否是可以进件的数据
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.PPCJDL).orElse(new PreOrder());
            if (!Objects.isNull(preOrder) && !Objects.isNull(preOrder.getPreOrderState())) {
                PpdOrderStatus ppdOrderStatusEnum = PpdConvert.INSTANCE.toPpdOrderStatusEnum(preOrder.getPreOrderState());
                creditApplyResponse.setStatus(ppdOrderStatusEnum.getCode());
                creditApplyResponse.setMsg(ppdOrderStatusEnum.getMsg());
                return creditApplyResponse;
            }

            if (PreOrderState.AUDIT_REJECT == preOrder.getPreOrderState()) {
                creditApplyResponse.setStatus(PpdOrderStatus.FAIL.getCode());
                creditApplyResponse.setMsg(PpdOrderStatus.FAIL.getMsg());
                return creditApplyResponse;
            }

            if (PreOrderState.AUDITING == preOrder.getPreOrderState() || PreOrderState.INIT == preOrder.getPreOrderState()) {
                PpdOrderStatus ppdOrderStatusEnum = PpdConvert.INSTANCE.toPpdOrderStatusEnum(preOrder.getPreOrderState());
                creditApplyResponse.setStatus(ppdOrderStatusEnum.getCode());
                creditApplyResponse.setMsg(PpdOrderStatus.PROCESSING.getMsg());
                return creditApplyResponse;
            }

            // 30天内授信失败检验
            List<Order> orders = queryThirtyDayCreditFailRecord(creditApplyRequest.getIdNo());
            if (!CollectionUtils.isEmpty(orders)) {
                creditApplyResponse.setStatus(PpdOrderStatus.FAIL.getCode());
                creditApplyResponse.setMsg(PpdOrderStatus.FAIL.getMsg());
                return creditApplyResponse;
            }

            //填充预订单
            preOrder = PpdConvert.INSTANCE.toPreOrder(preOrder, creditApplyRequest);

            //保存预订单
            preOrder = preOrderRepository.save(preOrder);

            //保存申请记录
            PpdCreditApplyRecord ppdCreditApplyRecord = ppdCreditApplyRecordRepository.findByLoanReqNo(orderNo);
            if (ppdCreditApplyRecord == null) {
                //新建
                ppdCreditApplyRecord = PpdConvert.INSTANCE.toCreditApplyRecord(creditApplyRequest);
            } else {
                //更新
                ppdCreditApplyRecord = PpdConvert.INSTANCE.toCreditApplyRecord(ppdCreditApplyRecord, creditApplyRequest);
            }
            ppdCreditApplyRecordRepository.save(ppdCreditApplyRecord);

            //判断是否存在在途订单
            String onOrder = checkService.onOrderOrRiskByCertNo(preOrder);
            if (StringUtil.isNotBlank(onOrder)) {
                preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
                preOrder.setIsReject(WhetherState.Y);
                preOrder.setRemark(onOrder);
                preOrderRepository.save(preOrder);
                throw new LvxinBizException("存在在途订单", LvxinResultCode.QUERY_DATA_WITHOUT);
            }

            logger.info("拍拍授信进件，项目限额配置校验");
            String message = loanLimitCheckService.checkPaiPaiCreditLimit(preOrder);
            if (StringUtil.isNotBlank(message)) {
                preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
                preOrder.setIsReject(WhetherState.Y);
                preOrder.setRemark(message);
                preOrderRepository.save(preOrder);
                creditApplyResponse.setStatus("00");
                return creditApplyResponse;
            }
            //注册用户
            UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
            if (Objects.isNull(userRegister)) {
                userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
            }
            //user_info
            UserInfo userInfo = PpdConvert.INSTANCE.toUserInfo(preOrder, ppdCreditApplyRecord);
            //user_ocr
            UserOcr userOcr = createUserOcr(preOrder, ppdCreditApplyRecord);
            //user_face
            UserFace userFace = createUserFace(preOrder, ppdCreditApplyRecord);
            //device
            UserDevice userDevice = createUserDevice(ppdCreditApplyRecord);
            //contactInfos
            List<UserContactInfo> contactInfos = createUserContactInfos(ppdCreditApplyRecord);
            UserRiskRecord riskRecord = userService.register(userInfo, userOcr, userFace, userDevice, contactInfos, FlowChannel.PPCJDL, preOrder.getApplyChannel());
            String userId = riskRecord.getUserId();
            //保存风控id
            preOrder.setRiskId(riskRecord.getId());
            preOrder.setOpenId(userId);
            preOrder = preOrderRepository.saveAndFlush(preOrder);
            //填充userId
            userRegister.setUserId(userId);
            userRegisterRepository.save(userRegister);
            //异步影像件下载
            mqService.submitCreditUserFileDownload(ppdCreditApplyRecord.getId());
            //拍拍 根据sourceCode区分资方是长银还是湖消 CJCYDL_PPD2和CJCYDL_PPD3 是长银  CJHBXJ_PPD2 和 CJHBXJ_PPD3 是湖消， 长银调用蚂蚁授信  湖消不调用
            //if (StringUtils.equals(ppdCreditApplyRecord.getSourceCode(),ApplyChannel.CJCYDL_PPD2.name())||StringUtils.equals(ppdCreditApplyRecord.getSourceCode(),ApplyChannel.CJCYDL_PPD3.name())) {
            if (preOrder.getBankChannel() != BankChannel.HXBK) {
                //异步同步蚂蚁接口
                mqService.submitMayiAccess(preOrder.getId());
            }
            //更新预订单为审核中
            preOrder.setPreOrderState(PreOrderState.AUDITING);
            preOrderRepository.saveAndFlush(preOrder);
            creditApplyResponse.setStatus("00");
            creditApplyResponse.setMsg("请求成功");
        } catch (InterruptedException e) {
            creditApplyResponse.setStatus(PpdOrderStatus.SYS_ERROR.getCode());
            creditApplyResponse.setErrCode(PpdErrorCode.ER00.name());
            creditApplyResponse.setMsg("系统异常,请联系管理员");
            return creditApplyResponse;
        } catch (Exception e) {
            logger.error("拍拍贷授信失败,orderNo:" + orderNo, e);
            throw e;
        } finally {
            lock.unlock();
        }
        return creditApplyResponse;
    }

    public List<Order> queryThirtyDayCreditFailRecord(String certNo) {
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(thirty);
        return orderRepository.queryThirtyDayCreditFailRecord(OrderState.CREDIT_FAIL, certNo, failCreditDate);
    }

    /**
     * 判断年龄是否有效
     *
     * @param certNo 身份证号
     * @return 结果
     */
    private static final DateTimeFormatter AGE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final int ID_AGE_START = 6;
    private static final int ID_AGE_END = 14;
    private static final int AGE_LOWER_LIMIT = 22;
    private static final int AGE_HIGH_LIMIT = 55;

    public boolean isValidAge(String certNo) {
        String ageStr = certNo.substring(ID_AGE_START, ID_AGE_END);
        LocalDate birth = LocalDate.parse(ageStr, AGE_FORMAT);
        long age = AgeUtil.calcAge(birth);
        return age >= AGE_LOWER_LIMIT && age <= AGE_HIGH_LIMIT;
    }

    private void checkApprovalParameters(ApprovalRequest approvalRequest) {
        String certNo = approvalRequest.getIdCard();
        if (!isValidAge(certNo)) {
            throw new LvxinBizException(LvxinResultCode.CREDIT_AGE_LIMIT.getMsg());
        }
    }

    /**
     * 查询授信结果
     *
     * @param creditQueryRequest
     * @return CreditQueryResponse
     */
    public CreditQueryResponse creditResultQuery(CreditQueryRequest creditQueryRequest) {
        //查询预订单
        String orderNo = creditQueryRequest.getLoanReqNo();
        PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.PPCJDL)
            .orElseThrow(() -> new PpdBizException(PpdResultCode.SYSTEM_ERROR));

        CreditQueryResponse creditQueryResponse = new CreditQueryResponse();

        switch (preOrder.getPreOrderState()) {
            case INIT, AUDITING -> {
                creditQueryResponse.setStatus(PpdOrderStatus.PROCESSING.getCode());   // 审核中
                creditQueryResponse.setMsg(PpdOrderStatus.PROCESSING.getMsg());
                return creditQueryResponse;
            }
            case AUDIT_PASS -> {
                Order order = orderService.findByRiskId(preOrder.getRiskId());
                if (Objects.isNull(order)) {
                    //风控通过,可能订单还没保存
                    creditQueryResponse.setStatus(PpdOrderStatus.PROCESSING.getCode());   // 审核中
                    creditQueryResponse.setMsg(PpdOrderStatus.PROCESSING.getMsg());
                    return creditQueryResponse;
                }

                if (OrderState.LOAN_CANCEL == order.getOrderState()) {
                    creditQueryResponse.setStatus(PpdOrderStatus.FAIL.getCode());   // 授信过期
                    creditQueryResponse.setMsg(PpdOrderStatus.FAIL.getMsg());
                    return creditQueryResponse;
                }
                creditQueryResponse.setStatus(PpdOrderStatus.SUCCESS.getCode()); // 授信通过
                creditQueryResponse.setMsg(PpdOrderStatus.SUCCESS.getMsg());
                return creditQueryResponse;
            }
            case AUDIT_REJECT -> {
                if (preOrder.getRemark().equals("授信累计额度已达项目限额上限")) {
                    creditQueryResponse.setStatus(PpdOrderStatus.FAIL.getCode()); // 拒绝
                    creditQueryResponse.setMsg(preOrder.getRemark());
                    return creditQueryResponse;
                }else{
                    creditQueryResponse.setStatus(PpdOrderStatus.FAIL.getCode()); // 拒绝
                    creditQueryResponse.setMsg(PpdOrderStatus.FAIL.getMsg());
                    return creditQueryResponse;
                }
            }
            default -> {
            }
        }
        return creditQueryResponse;
    }


    /**
     * 查询授信额度
     *
     * @param quotaQueryRequest
     * @return
     */
    public QuotaQueryResponse queryQuota(QuotaQueryRequest quotaQueryRequest) {
        QuotaQueryResponse response = new QuotaQueryResponse();
        //查询预订单
        String orderNo = quotaQueryRequest.getPartnerUserId();
        PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.LVXIN)
            .orElseThrow(() -> new LvxinBizException(LvxinResultCode.PRE_ORDER_NOT_EXIST));

        switch (preOrder.getPreOrderState()) {
            case INIT, AUDITING -> {
                response.setStatus(LvxinQuotaStatus.PROCESSING.getCode()); // 获额处理中
                return response;
            }
            case AUDIT_PASS -> {
                Order order = orderService.findByRiskId(preOrder.getRiskId());
                if (Objects.isNull(order)) {
                    //风控通过,可能订单还没保存
                    response.setStatus(LvxinQuotaStatus.PROCESSING.getCode()); // 审核中
                    return response;
                }
                if (OrderState.LOAN_CANCEL == order.getOrderState()) {
                    response.setStatus(LvxinQuotaStatus.INVALID.getCode()); // 额度失效
                    return response;
                }
                response.setStatus(LvxinQuotaStatus.AVAILABLE.getCode()); // 额度可用
                response.setCreditAmount(order.getApproveAmount());

                response.setTerms(List.of(order.getApplyPeriods()));
                // 计算在贷
                BigDecimal loanBalance = loanBalance(order.getUserId(), order.getRiskId());
                BigDecimal availCreditQuota = order.getApproveAmount().subtract(loanBalance);
                if (availCreditQuota.compareTo(BigDecimal.ZERO) < 0) {
                    availCreditQuota = BigDecimal.ZERO;
                }
                response.setCanBorrowAmount(availCreditQuota);
            }
            case AUDIT_REJECT -> {
                response.setStatus(LvxinQuotaStatus.REJECT.getCode()); // 拒绝
                response.setReason(BaseConstants.DEFAULT_RISK_REJECT_REASON);
                return response;
            }
            default -> {
            }
        }
        return response;
    }


    /**
     * 绑卡申请
     */
    public BindCardApplyResponse bindCardApply(BindCardApplyRequest applyRequest) {
        String orderNo = applyRequest.getPartnerUserId();
        String lockKey = FlowChannel.LVXIN.name() + "_bind_apply_" + orderNo;
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (locked) {
                Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(orderNo, FlowChannel.LVXIN);
                if (order == null) {
                    logger.error("绑卡申请,订单不存在,orderNo:{}", orderNo);
                    throw new LvxinBizException(LvxinResultCode.PRE_ORDER_NOT_EXIST);
                }
                Loan loan = loanRepository.findByOrderId(order.getId());
                if (loan == null) {
                    //贷前绑卡
                    return beforeLoanBind(applyRequest, order);
                } else {
                    //贷后绑卡
                    return afterLoanBind(applyRequest, loan, order);
                }
            } else {
                throw new LvxinBizException(LvxinResultCode.REPEAT_SUBMIT);
            }
        } catch (InterruptedException e) {
            // ignore;
        } finally {
            lock.unlock();
        }
        throw new LvxinBizException(LvxinResultCode.BIND_CARD_ERROR);
    }


    /**
     * 贷前绑卡处理
     */
    private BindCardApplyResponse beforeLoanBind(BindCardApplyRequest applyRequest, Order order) {
        //发起平台绑卡
        BindCardRecord bindCardRecord = platformCardService.bindApply(applyRequest.getIdCard(),
            applyRequest.getCardNo(),
            applyRequest.getCardPhone(),
            applyRequest.getName(),
            order.getUserId(),
            order,
            ProtocolChannel.BF);
        return bindApplySuccessProcess(bindCardRecord, order);
    }

    private BindCardApplyResponse afterLoanBind(BindCardApplyRequest applyRequest, Loan loan, Order order) {
        //贷后直接换绑
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(applyRequest.getCardPhone());
        exchangeCardApplyReq.setCardNo(applyRequest.getCardNo());
        exchangeCardApplyReq.setBoundSide(BoundSide.PLATFORM);
        BindCardRecord bindCardRecord = platformCardService.bindApplyLoan(loan, exchangeCardApplyReq);
        return bindApplySuccessProcess(bindCardRecord, order);
    }

    private BindCardApplyResponse bindApplySuccessProcess(BindCardRecord bindCardRecord, Order order) {
        if (bindCardRecord == null) {
            throw new LvxinBizException(LvxinResultCode.BIND_CARD_RECORD_NOT_EXIST);
        }

        ProcessState state = bindCardRecord.getState();
        if (ProcessState.FAILED == state) {
            throw new LvxinBizException(bindCardRecord.getFailReason());
        }

        if (ProcessState.PROCESSING == state) {
            BindCardApplyResponse response = new BindCardApplyResponse();
            response.setSerialNumber(bindCardRecord.getConfirmOrderNo());
            response.setNum(NumConstants.SIX);
            //对方根据这个1，发起绑卡确认
            response.setNeedSmsCode(1);
            return response;
        }
        throw new LvxinBizException(LvxinResultCode.BIND_CARD_ERROR);
    }

    private LvxinRepayResponse exchangeBindApply(LvxinRepayRequest request, Loan loan) {
        LvxinRepayResponse response = new LvxinRepayResponse();
        String orderNo = request.getPartnerUserId();
        //先将之前处理中的置为失败
        LvxinRebindRecord rebindRecordCapital = lvxinRebindRecordRepository.
            findByCreditIdAndStateAndBoundSide(orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
        if (rebindRecordCapital != null) {
            rebindRecordCapital.setState(ProcessState.FAILED);
            lvxinRebindRecordRepository.save(rebindRecordCapital);
        }

        BindCardRecord capitalBindResult = capitalBindApply(loan, orderNo);
        //已签约,直接发起还款
        if (ProcessState.SUCCEED == capitalBindResult.getState()) {
            //绑卡状态变更
            LvxinRebindRecord rebindRecord = lvxinRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
            rebindRecord.setState(ProcessState.SUCCEED);
            lvxinRebindRecordRepository.save(rebindRecord);
            //还款
            String repayNo = lvxinOnlineRepay(request);
            response.setNeedSmsCode(0);
            return response;
        }
        //绑卡申请成功,后续调用还款验证码接口
        if (ProcessState.PROCESSING == capitalBindResult.getState()) {
            response.setNeedSmsCode(1);
            return response;
        }

        logger.error("绿信还款异常: 存在换绑,资方绑卡申请返回失败,reason:{}", capitalBindResult.getFailReason());
        throw new LvxinBizException("还款失败");
    }


    /**
     * 绑卡确认
     *
     * @param confirmRequest 请求
     * @return 结果
     */
    public BindCardConfirmResponse bindConfirm(BindCardConfirmRequest confirmRequest) {
        BindCardConfirmResponse response = new BindCardConfirmResponse();

        String orderNo = confirmRequest.getPartnerUserId();
        String serialNumber = confirmRequest.getSerialNumber();
        String smsCode = confirmRequest.getVerifyCode();

        String lockKey = FlowChannel.LVXIN.name() + "_bind_confirm_" + orderNo;
        Locker lock = lockService.getLock(lockKey);
        BindCardRecord bindCardRecord = null;
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (locked) {
                BindCardRecord byConfirmOrderNo = bindCardRecordRepository.findByConfirmOrderNo(serialNumber);
                if (byConfirmOrderNo == null) {
                    throw new LvxinBizException(LvxinResultCode.BIND_CARD_RECORD_NOT_EXIST);
                }
                Optional<UserBankCard> userBankCardOptional = userBankCardRepository.findById(byConfirmOrderNo.getId());
                if (userBankCardOptional.isPresent()) {
                    logger.error("验证码校验接口重复提交,serialNumber:{},smsCode:{}", serialNumber, smsCode);
                    throw new LvxinBizException(LvxinResultCode.VERIFY_CODE_EXPIRED);
                }
                if (BoundSide.PLATFORM == byConfirmOrderNo.getBoundSide()) {
                    bindCardRecord = platformCardService.bindConfirm(smsCode, byConfirmOrderNo.getId());
                } else {
                    capitalCardService.bindConfirm(byConfirmOrderNo.getId(), smsCode);
                    //资方绑卡结束后,会更新bindCardRecord状态和失败原因
                    bindCardRecord = bindCardRecordRepository.findById(byConfirmOrderNo.getId()).orElseThrow();
                }

                if (bindCardRecord == null) {
                    throw new LvxinBizException(LvxinResultCode.SYSTEM_ERROR);
                }
                // 绑卡确认成功
                if (bindCardRecord.getState() == ProcessState.SUCCEED) {
                    processSuccessConfirm(bindCardRecord, orderNo, response);
                    return response;
                }

                String msg = bindCardRecord.getFailReason();
                throw new LvxinBizException(StringUtil.isBlank(msg) ? "绑卡验证失败" : msg);
            } else {
                throw new LvxinBizException(LvxinResultCode.REPEAT_SUBMIT);
            }
        } catch (InterruptedException e) {
            logger.error("绿信绑卡确认失败,orderNo:" + orderNo, e);
            throw new LvxinBizException(LvxinResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    private void processSuccessConfirm(BindCardRecord bindCardRecord, String orderNo, BindCardConfirmResponse response) {
        response.setPartnerCardId(bindCardRecord.getId());
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(orderNo, FlowChannel.LVXIN);
        if (bindCardRecord.getBoundSide() == BoundSide.CAPITAL) {
            order.setBindCapitalCardState(WhetherState.Y);
            order = orderRepository.save(order);
            response.setNeedSmsCode(0);
            response.setPartnerCardId(order.getLoanCardId()); //平台绑卡的ID
        } else {
            //更新Order、Credit的绑卡Id
            order = platformCardSuccessProcess(bindCardRecord, order);
            Loan loan = loanService.findByOrderId(order.getId());
            //判断是否贷后换绑卡
            if (loan != null && loan.getLoanState().equals(ProcessState.SUCCEED)) {
                UserBankCard userBankCard = userBankCardRepository.findById(bindCardRecord.getId())
                    .orElseThrow(() -> new LvxinBizException(LvxinResultCode.BIND_CARD_RECORD_NOT_EXIST));
                LvxinRebindRecord rebindRecord = lvxinRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                    orderNo, ProcessState.PROCESSING, BoundSide.PLATFORM);
                if (rebindRecord != null) {
                    rebindRecord.setState(ProcessState.FAILED);
                    lvxinRebindRecordRepository.save(rebindRecord);
                }
                loan.setRepayCardId(userBankCard.getId());
                loanService.saveLoan(loan);
                //贷后换绑,资方绑卡通过还款接口发起
                response.setNeedSmsCode(0);
                response.setSerialNumber(userBankCard.getId());
            } else {
                response.setSerialNumber(bindCardRecord.getConfirmOrderNo());
                response.setPartnerCardId(order.getLoanCardId()); //平台绑卡的ID
                response.setNeedSmsCode(0);
            }
        }
    }

    /**
     * 借款试算
     *
     * @param request
     * @return
     */
    public LoanTrialResponse loanTrial(LoanTrialRequest request) {
        LoanTrialResponse response = new LoanTrialResponse();
        String orderNo = request.getPartnerUserId();
        Order order = orderService.query(orderNo, null, FlowChannel.LVXIN);
        if (Objects.isNull(order)) {
            logger.error("订单不存在,orderNo:" + orderNo);
            throw new LvxinBizException(LvxinResultCode.PRE_ORDER_NOT_EXIST);
        }
        List<RepayPlanItem> repayPlanItems = CommonRepayPlanCalc.calcRepayPlan(order);
        //计划还款日
        LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
        for (RepayPlanItem item : repayPlanItems) {
            item.setCustomRepayDate(now.toLocalDate().plusMonths(item.getPeriod()));
        }
        //转换为绿信还款计划，并格式化金额、日期
        List<LvxinRepayPlan> repayPlans = LvxinConvert.INSTANCE.toTrialRepayPlanList(repayPlanItems);
        //绿信还款计划
        response.setRepayPlanList(repayPlans);
        //返回结果处理
        BigDecimal repayTotal = repayPlanItems.stream().map(RepayPlanItem::getCustomTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principalTotal = repayPlanItems.stream().map(RepayPlanItem::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal interestTotal = repayPlanItems.stream().map(RepayPlanItem::getInterestAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalConsultFee = repayPlanItems.stream().map(RepayPlanItem::getConsultAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

        //确认金额
        response.setTotalAmount(repayTotal);
        response.setTotalPrincipal(principalTotal);
        response.setTotalInterest(interestTotal);
        response.setTotalRightFee(BigDecimal.ZERO);
        response.setExecuteRate(order.getApproveRate().getRate().multiply(new BigDecimal("100"))
            .setScale(NumConstants.TWO, RoundingMode.HALF_DOWN).toPlainString() + "%");
        response.setTotalConsultFee(totalConsultFee);
        response.setTotalOverdue(BigDecimal.ZERO);
        return response;
    }

    /**
     * 获取借款确认页面Url
     *
     * @param request
     * @return
     */
    public LoanApplyResponse loanApply(LoanApplyRequest request) {

        LoanApplyResponse response = new LoanApplyResponse();

        String loanReqNo = request.getLoanReqNo();

        Locker lock = lockService.getLock(FlowChannel.PPCJDL.name() + LOAN_APPLY + loanReqNo + request.getSourceCode());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, DEFAULT_LOCK_RELEASE_TIME);
            if (!locked) {
                response.setStatus("01");
                response.setMsg("重复提交");
                return response;
            }
            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
            if (applyChannel == null) {
                throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
            }
            // 更新放款申请表
            PpdLoanApplyRecord loanApplyRecord = loanApplyRecordRepository.findByLoanReqNoAndSourceCode(loanReqNo, request.getSourceCode());
            //查询是否已经存在放款申请数据，存在就返回成功
            if (!Objects.isNull(loanApplyRecord)) {
                response.setStatus(PpdRepayStatus.SUCCESS.getCode());
                response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
                return response;
            }
            Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(loanReqNo, FlowChannel.PPCJDL);
            if (OrderState.AUDIT_PASS != order.getOrderState()) {
                logger.error("【新流程】授信状态为:{},无法发起借款,orderId:{},loanReqNo:{}", order.getOrderState(), order.getId(), loanReqNo);
                response.setStatus(PpdRepayStatus.FAIL.getCode());
                response.setMsg(PpdRepayStatus.FAIL.getDesc());
                return response;
            }
            if (Objects.isNull(loanApplyRecord)) {
                loanApplyRecord = PpdConvert.INSTANCE.toLoanApplyRecord(request);
            } else {
                loanApplyRecord = PpdConvert.INSTANCE.toLoanApplyRecord(loanApplyRecord, request);
            }
            loanApplyRecordRepository.save(loanApplyRecord);
            //直接发起放款
            BindCardRecord finalRecord = new BindCardRecord();
            finalRecord.setUserId(order.getUserId());
            finalRecord.setState(ProcessState.PROCESSING);
            // BAOFOOXY：宝付支付（暂时只支持宝付）只有当与合作方签约共享时传
            finalRecord.setChannel(ProtocolChannel.BF);
            finalRecord.setCertNo(request.getIdNo());
            // 还款人卡信息
            finalRecord.setPhone(request.getRepayBankMobile());
            finalRecord.setName(request.getRepayAcctName());
            finalRecord.setBankCardNo(request.getRepayBankAcct());
            finalRecord.setAgreeNo(request.getChannelRepayId());
            //调用接口查询卡归属码值
            CardBin query = alipayCardBinService.query(request.getBankAcct());
            if (ObjectUtils.isEmpty(query)) {
                response.setStatus("01");
                response.setMsg(ResultCode.CARD_NOT_SUPPORT.getMsg());
                return response;
            }
            finalRecord.setBankCode(query.getBankAbbr());
            finalRecord.setBankName(query.getName());
            BindCardRecord save = bindCardRecordRepository.save(finalRecord);
            BindCardRelation bindCardRelation = new BindCardRelation();
            bindCardRelation.setRelatedId(save.getId());
            bindCardRelation.setBindStage(LoanStage.LOAN);
            bindCardRelation.setBindCardApplyId(finalRecord.getId());
            bindCardRelation.setUserId(order.getUserId());

            relationRepository.save(bindCardRelation);
            // 绑卡成功, 插入用户绑卡记录
            UserBankCard userBankCard = new UserBankCard();
            userBankCard.setUserId(order.getUserId());
            userBankCard.setCardNo(request.getRepayBankAcct());
            userBankCard.setCardName(request.getRepayAcctName());
            userBankCard.setPhone(request.getRepayBankMobile());
            userBankCard.setCertNo(request.getIdNo());
            userBankCard.setChannel(ProtocolChannel.BF);
            // TODO  这个商户号怎么处理
            userBankCard.setMerchantNo("PPCJDL");//lvxin
            userBankCard.setAgreeNo(request.getChannelRepayId());
            userBankCard.setBankCode(finalRecord.getBankCode());
            userBankCard.setBankName(finalRecord.getBankName());
            userBankCardRepository.save(userBankCard);

            order.setLoanPurpose(PpdConvert.toLoanPurpose(request.getLoanPurpose()));
            order.setId(order.getId());
            order.setLoanCardId(userBankCard.getId());
            order.setOrderSubmitState(WhetherState.Y);
//            order.setApplyChannel(ApplyChannel.getApplyChannel(request.getSourceCode()).getCode());
            order = orderRepository.save(order);

            orderService.apply(order.getId(), order.getRightsMarking());

            //保存绑卡baof的卡信息
            orderBindCardRecordRepository.findById(order.getId()).ifPresent(record -> {
                record.setFirstCardId(userBankCard.getId());
                orderBindCardRecordRepository.save(record);
            });
        } catch (InterruptedException e) {
            response.setStatus(PpdOrderStatus.SYS_ERROR.getCode());
            response.setErrCode(PpdErrorCode.ER00.name());
            response.setMsg("系统异常,请联系管理员");
            return response;
        } catch (Exception e) {
            logger.error("拍拍贷放款失败,loanReqNo:" + loanReqNo, e);
            throw e;
        } finally {
            lock.unlock();
        }

        response.setStatus("00");
        response.setMsg("请求成功");
        return response;
    }


    /**
     * 获取权益页面Url
     *
     * @param request
     * @return
     */
    public LvxinGetRightsUrlResponse getRightsUrl(LvxinGetRightsUrlRequest request) {
        LvxinGetRightsUrlResponse response = new LvxinGetRightsUrlResponse();
        return response;
    }

    /**
     * 查询放款结果
     *
     * @param request
     * @return
     */
    public LoanQueryResponse queryLoanResult(LoanQueryRequest request) {
        LoanQueryResponse response = new LoanQueryResponse();
        // 回到路由,处理后续处理后续
        String loanReqNo = request.getLoanReqNo();
        Loan loan = null;
        Order order = orderRepository.findByOuterOrderId(loanReqNo);
        if (Objects.isNull(order)) {
            loan = loanRepository.findById(loanReqNo).orElse(null);
        } else {
            loan = loanRepository.findByOrderId(order.getId());
        }
        if (Objects.isNull(loan) && Objects.isNull(order)) {
            logger.error("订单不存在,orderNo:" + loanReqNo);
            response.setStatus(PpdOrderStatus.SYS_ERROR.getCode());
            response.setErrCode(PpdErrorCode.ER00.name());
            response.setMsg("订单不存在");
        }
        return queryLoanResult(loan, order);
    }

    /**
     * 查询放款结果
     *
     * @param loan
     * @param order
     * @return
     */
    public LoanQueryResponse queryLoanResult(Loan loan, Order order) {

        if (Objects.isNull(order)) {
            order = orderRepository.findById(loan.getOrderId()).orElseThrow(() -> new PpdBizException("订单不存在"));
        }

        LoanQueryResponse response = new LoanQueryResponse();
        try {

            String ppdLoanStatus = PpdConvert.toPpdLoanStatus(order.getOrderState());
            response.setStatus(ppdLoanStatus);
            switch (order.getOrderState()) {
                case INIT, LOANING:
                    response.setLoanAmt(order.getApproveAmount());
                    response.setMsg("处理中");
                    break;
                case LOAN_FAIL:
                    // TODO 需要确定
                    //封禁期:D+1天的早上7点
                    if (order.getRemark() != null && order.getRemark().contains("头寸不足")) {
                        response.setMsg(order.getRemark());
                    } else {
                        if (Objects.nonNull(loan)) {
                            response.setMsg(loan.getFailReason());
                            loanFailFollowRepository.findByLoanId(loan.getId()).ifPresent(follow -> {
                                response.setMsg(follow.getFailReason());
                            });
                        }
                    }
                    break;
                case LOAN_PASS:
                    // 该用户放款成功的订单
                    response.setLoanAmt(order.getApproveAmount());
                    response.setCashDate(loan.getLoanTime());
                    response.setLoanAmt(loan.getAmount());
                    break;
            }

        } catch (Exception e) {
            logger.error("拍拍贷放款结果查询异常", e);
            response.setStatus(PpdOrderStatus.SYS_ERROR.getCode());
            response.setErrCode(PpdErrorCode.ER00.name());
            response.setMsg("系统异常,请联系管理员");
        }

        return response;
    }

    /**
     * 查询还款计划
     *
     * @param
     * @return
     */
    public RepayPlanQueryResponse queryRepayPlan(RepayPlanQueryRequest request) {
        RepayPlanQueryResponse response = new RepayPlanQueryResponse();

        String loanReqNo = request.getLoanReqNo();
        String sourceCode = request.getSourceCode();

        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(sourceCode);
        if (applyChannel == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }

        Loan loan = loanRepository.findByOuterLoanIdAndFlowChannel(loanReqNo,
            FlowChannel.PPCJDL).orElseThrow(() -> new LvxinBizException("订单不存在"));

        if (ProcessState.SUCCEED != loan.getLoanState()) {
            throw new LvxinBizException("放款状态非成功");
        }
        List<RepayPlan> repayPlanList = repayPlanRepository.findByLoanId(loan.getId());

        List<RepayPlanQueryResponse.RepayPlan> collect = repayPlanList.stream().map(item -> {
            RepayPlanQueryResponse.RepayPlan repayPlan = new RepayPlanQueryResponse.RepayPlan();
            BigDecimal actConsultFee = AmountUtil.safeAmount(item.getActConsultFee());
            BigDecimal consultFee = AmountUtil.safeAmount(item.getConsultFee());
            BigDecimal actPenaltyAmt = AmountUtil.safeAmount(item.getActPenaltyAmt());
            BigDecimal penaltyAmt = AmountUtil.safeAmount(item.getPenaltyAmt());
            repayPlan.setTotalTerms(repayPlanList.size()); // 总期数
            repayPlan.setTerm(item.getPeriod());
            repayPlan.setDateDue(item.getPlanRepayDate());
            repayPlan.setTotalAmt(AmountUtil.safeAmount(item.getAmount()));
            repayPlan.setPrinAmt(AmountUtil.safeAmount(item.getPrincipalAmt()));
            repayPlan.setIntAmt(AmountUtil.safeAmount(item.getInterestAmt()));
            repayPlan.setOintAmt(penaltyAmt);
            repayPlan.setZhibaoFee(AmountUtil.safeAmount(item.getGuaranteeAmt()));
            repayPlan.setPoundageAmt(consultFee);
            repayPlan.setActualTotalAmt(AmountUtil.safeAmount(item.getActAmount()));
            repayPlan.setActualPrinAmt(AmountUtil.safeAmount(item.getActPrincipalAmt()));
            repayPlan.setActualIntAmt(AmountUtil.safeAmount(item.getActInterestAmt()));
            repayPlan.setActualOintAmt(actPenaltyAmt);
            repayPlan.setActualZhibaoFee(AmountUtil.safeAmount(item.getActGuaranteeAmt()));
            repayPlan.setActualPoundageAmt(actConsultFee);
            // 减免金额
            BigDecimal reductPoundage = actConsultFee.compareTo(BigDecimal.ZERO) > 0 ? consultFee.subtract(actConsultFee) : BigDecimal.ZERO;
            BigDecimal reductOintAmt = actPenaltyAmt.compareTo(BigDecimal.ZERO) > 0 ? penaltyAmt.subtract(actPenaltyAmt) : BigDecimal.ZERO;
            BigDecimal reductTotalAmt = reductPoundage.add(reductOintAmt);
            repayPlan.setReductPoundage(reductPoundage);
            repayPlan.setReductOintAmt(reductOintAmt);
            repayPlan.setReductTotalAmt(AmountUtil.safeAmount(reductTotalAmt));
            repayPlan.setOintUpdatetime(item.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return repayPlan;
        }).sorted((Comparator.comparing(RepayPlanQueryResponse.RepayPlan::getTerm))).collect(Collectors.toList());

        response.setStatus("00");
        response.setPlanList(collect);
        // 实际计算还款计划的利率，用于校验双方使用的利率是否一致，如双方约定的利率为8%，则传8
        Order order = orderRepository.findOrderById(loan.getOrderId());
        response.setRate(order.getApproveRate().getRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));

        return response;
    }


    private void setNormalResponse(LvxinRepayPlanQueryResponse response, Loan loan, List<RepayPlan> repayPlanList) {
        response.setPartnerOrderNo(loan.getId());
        response.setLoanGid(""); //不能给null
        response.setRepaymentStatus(getLvxinRepayStatus(repayPlanList, loan));
        response.setIsOverdue(checkOverdue(repayPlanList));
        response.setStage(loan.getPeriods());
    }

    /**
     * 还款试算
     *
     * @param request
     * @return
     */
    public LvxinRepayTrialResponseV2 trail(LvxinRepayTrialRequest request) {
        logger.info("绿信还款试算请求参数:{}", JsonUtil.toJsonString(request));
        checkParameters(request);
        String loanId = request.getLoanGid();
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new LvxinBizException(LvxinResultCode.LOAN_NOT_EXIST));
        // 还款时间段校验
        repayTrailCheck(loan);
        RepayPurpose repayPurpose = convertToRepayPurpose(request.getRepayType());
        int repayPeriod = request.getPeriod();
        // 如果是结清,传入最小未还的期次
        if (RepayPurpose.CLEAR == repayPurpose) {
            if (CollectionUtils.isEmpty(repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL))) {
                throw new LvxinBizException(LvxinResultCode.REPAY_PLAN_NORMAL_NOT_EXIST);
            }
        }
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            loanId, repayPeriod, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("绿信还款试算失败，本期已还款成功,loanId:{},periods:{}", loanId, repayPeriod);
            throw new LvxinBizException(LvxinResultCode.REPAY_PLAN_ALREADY_SUCCESS);
        }

        try {
            TrialResultVo resultVo = trialService.repayTrial(loanId, repayPurpose, repayPeriod, null);
            LvxinRepayTrialResponseV2 trialResponseV2 = LvxinConvert.INSTANCE.toRepayTrailResponseV2(resultVo);
            trialResponseV2.setPartnerOrderNo(loanId);
            trialResponseV2.setTotalAmt(trialResponseV2.getTotalAmt().setScale(2, RoundingMode.HALF_DOWN));
            trialResponseV2.setTotalConsultFee(trialResponseV2.getTotalConsultFee().setScale(2, RoundingMode.HALF_DOWN));
            logger.info("绿信还款试算返回:{}", JsonUtil.toJsonString(trialResponseV2));
            return trialResponseV2;
        } catch (BizException e) {
            logger.error("绿信还款试算异常", e);
            throw new LvxinBizException(e.getMessage(), LvxinResultCode.REPAY_TRIAL_ERROR);
        } catch (Exception e) {
            logger.error("绿信还款试算异常", e);
            throw new LvxinBizException(LvxinResultCode.SYSTEM_ERROR);
        }
    }

    private RepayPurpose convertToRepayPurpose(String repayType) {
        return switch (repayType) {
            case "1" -> RepayPurpose.CURRENT;
            case "2" -> RepayPurpose.CLEAR;
            default -> null;
        };
    }

    public LvxinRepayResponse repay(LvxinRepayRequest request) {
        LvxinRepayResponse response = new LvxinRepayResponse();
        checkParameters(request);
        String orderNo = request.getPartnerUserId();
        String loanId = request.getLoanGid();
        String repaymentGid = request.getRepaymentGid();
        //检查重复提交
        boolean existsRecord = lvxinRepayApplyRecordRepository.existsByOutRepayId(repaymentGid);
        if (existsRecord) {
            logger.error("绿信还款申请，重复提交，orderNo:{},loanId:{},outRepayId:{}", orderNo, loanId, repaymentGid);
            throw new LvxinBizException(LvxinResultCode.REPEAT_SUBMIT);
        }
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new LvxinBizException(LvxinResultCode.LOAN_NOT_EXIST));
        // 判断是否提前结清
        List<Integer> repayList = new ArrayList<>(Collections.singletonList(request.getPeriod()));
        RepayPurpose repayPurpose = RepayPurpose.toRepayType(request.getRepayType());
        int period = getMinPeriod(repayList, repayPurpose);
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("绿信还款申请失败，本期已还款成功，orderNo:{},loanId:{},periods:{}", orderNo, loanId, repayList);
            throw new LvxinBizException(LvxinResultCode.REPAY_PLAN_ALREADY_SUCCESS);
        }
        //保存还款记录
        LvxinRepayApplyRecord repayApplyRecord = lvxinRepayApplyRecordRepository.findByOutRepayId(request.getRepaymentGid()).orElse(null);
        if (repayApplyRecord == null) {
            repayApplyRecord = LvxinConvert.INSTANCE.toRepayApplyRecord(request);
            if (!ObjectUtils.isEmpty(request.getSyncBindCard())) {
                repayApplyRecord.setRepayCardId(request.getSyncBindCard().getAcctNo());
            }
            repayApplyRecord.setNeedSmsCode(WhetherState.N);
            repayApplyRecord = lvxinRepayApplyRecordRepository.save(repayApplyRecord);
        }
        //查询是否存在换绑记录
        LvxinRebindRecord rebindRecord = lvxinRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
            orderNo, ProcessState.PROCESSING, BoundSide.PLATFORM);
        if (rebindRecord == null) {
            // 没有贷后换绑卡, 直接发起还款
            String repayNo = lvxinOnlineRepay(request);
            response.setNeedSmsCode(0);
            return response;
        }

        repayApplyRecord.setNeedSmsCode(WhetherState.Y);
        lvxinRepayApplyRecordRepository.save(repayApplyRecord);

        //存在贷后换绑卡
        String lockKey = FlowChannel.LVXIN.name() + "_bind_apply_" + rebindRecord.getCreditId();
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("绿信还款申请，重复提交，orderNo:{}", orderNo);
                throw new LvxinBizException(LvxinResultCode.REPEAT_SUBMIT);
            }

            return exchangeBindApply(request, loan);
        } catch (Exception e) {
            logger.error("绿信还款异常: 存在换绑,资方绑卡申请处理失败", e);
            throw new LvxinBizException(LvxinResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    private int getMinPeriod(List<Integer> repayList, RepayPurpose repayPurpose) {
        //当期还款只传1期
        int repayPeriod = repayList.get(0);
        if (RepayPurpose.CLEAR.equals(repayPurpose)) {
            //取最早的一期
            repayList = repayList.stream().sorted().toList();
            repayPeriod = repayList.get(0);
        }
        return repayPeriod;
    }


    public LvxinRepayVerifyResponse checkRepayVerifyCode(LvxinRepayVerifyRequest request) {
        String orderNo = request.getPartnerUserId();
        String smsCode = request.getVerifyCode();
        String repaymentGid = request.getRepaymentGid();
        LvxinRebindRecord rebindRecord = lvxinRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
            orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
        if (Objects.isNull(rebindRecord)) {
            throw new LvxinBizException(LvxinResultCode.VERIFY_CODE_EXPIRED);
        }
        BindCardRecord bindCardRecord = bindCardRecordRepository.findById(rebindRecord.getBindCardRecordId()).orElseThrow();

        Optional<UserBankCard> userBankCardOptional = userBankCardRepository.findById(bindCardRecord.getId());
        if (userBankCardOptional.isPresent()) {
            logger.error("还款换绑验证码校验接口重复提交,orderNo:{},smsCode:{}", orderNo, smsCode);
            throw new LvxinBizException(LvxinResultCode.VERIFY_CODE_EXPIRED);
        }
        try {
            bindCardRecord = capitalCardService.bindExchangeConfirm(bindCardRecord, smsCode);
            //已签约,直接发起还款
            if (ProcessState.SUCCEED == bindCardRecord.getState()) {
                //绑卡状态变更
                rebindRecord.setState(ProcessState.SUCCEED);
                lvxinRebindRecordRepository.save(rebindRecord);
                LvxinRebindRecord platformRecord = lvxinRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                    orderNo, ProcessState.PROCESSING, BoundSide.PLATFORM);
                platformRecord.setState(ProcessState.SUCCEED);
                lvxinRebindRecordRepository.save(platformRecord);
                LvxinRepayApplyRecord repayApplyRecord = lvxinRepayApplyRecordRepository.findByOutRepayId(repaymentGid).orElseThrow();
                LvxinRepayRequest lvxinRepayRequest = LvxinConvert.INSTANCE.toLvxinRepayRequest(repayApplyRecord);
                //还款
                String repayNo = lvxinOnlineRepay(lvxinRepayRequest);
                LvxinRepayVerifyResponse response = new LvxinRepayVerifyResponse();
                response.setNeedSmsCode(0);
                return response;
            }
            logger.error("绿信还款短信验证异常,reason:{}", bindCardRecord.getFailReason());
            throw new LvxinBizException("短信验证失败");
        } catch (Exception e) {
            logger.error("绿信还款短信验证异常", e);
            throw new LvxinBizException("短信验证失败");
        }
    }


    private String lvxinOnlineRepay(LvxinRepayRequest request) {
        String loanId = request.getLoanGid();
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new LvxinBizException(LvxinResultCode.LOAN_NOT_EXIST));
        // 判断是否提前结清
        RepayPurpose repayPurpose = RepayPurpose.toRepayType(request.getRepayType());
        //当期还款只传1期
        int repayPeriod = request.getPeriod();

//        OnlineRepayApplyRequest repayApplyRequest = LvxinConvert.INSTANCE.toOnlineApplyRequest(request, loanId, repayPeriod, repayPurpose);
//        repayApplyRequest.setOtuRepayNo(request.getRepaymentGid());
//
//        String repayNo = repayService.online(repayApplyRequest);
//        logger.info("绿信 还款响应,loan:{}, customRepayRecord id:{}", loanId, repayNo);
        //线上线下逻辑
        String repayNo = "";
        if (RepayMode.ONLINE.name().equals(request.getRepayMode())) {
            OnlineRepayApplyRequest repayApplyRequest = LvxinConvert.INSTANCE.toOnlineApplyRequest(request, loanId, repayPeriod, repayPurpose);
            repayApplyRequest.setOtuRepayNo(request.getRepaymentGid());
            repayService.online(repayApplyRequest);
            logger.info("绿信 还款响应,loan:{}, customRepayRecord id:{}", loanId, repayNo);
        } else {
            RepayApplyDto repayApplyReq = new RepayApplyDto();
            repayApplyReq.setAmount(request.getRepayTotalAmount());
            repayApplyReq.setOrderId(loan.getOrderId());
            repayApplyReq.setRepayPurpose(com.jinghang.ppd.api.enums.RepayPurpose.valueOf(repayPurpose.toString()));
            repayApplyReq.setPeriod(repayPeriod);
            OfflineRepayApplyRequest offlineRepayApply = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
            offlineRepayApply.setWriteOffType(WriteOffTypeEnum.DIRECT);
            offlineRepayApply.setOuterRepayNo(request.getRepaymentGid());
            repayService.offline(offlineRepayApply);
        }
        return repayNo;
    }

    private BindCardRecord capitalBindApply(Loan loan, String orderNo) {
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(orderNo, FlowChannel.LVXIN);
        UserBankCard userBankCard = userBankCardRepository.findById(order.getLoanCardId()).orElseThrow();
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(userBankCard.getPhone());
        exchangeCardApplyReq.setCardNo(userBankCard.getCardNo());
        exchangeCardApplyReq.setBoundSide(BoundSide.CAPITAL);
        exchangeCardApplyReq.setCardName(userBankCard.getCardName());
        CardBin cardBin = platformCardService.queryCardBin(userBankCard.getCardNo());
        if (cardBin == null) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }
        String bankAbbr = cardBin.getBankAbbr();
        exchangeCardApplyReq.setBankCode(bankAbbr);
        exchangeCardApplyReq.setBankName(cardBin.getShortName());
        //资方换绑卡
        BindCardRecord bindCardRecord = capitalCardService.bindExchangeApply(loan, exchangeCardApplyReq);
        LvxinRebindRecord rebindRecord = buildLvxinRebindRecord(loan, bindCardRecord, orderNo);
        lvxinRebindRecordRepository.save(rebindRecord);
        return bindCardRecord;
    }

    private static LvxinRebindRecord buildLvxinRebindRecord(Loan byOuterLoanId, BindCardRecord bindCardRecordCapitalRe, String orderNo) {
        LvxinRebindRecord rebindRecord = new LvxinRebindRecord();
        rebindRecord.setState(ProcessState.PROCESSING);
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(byOuterLoanId.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecordCapitalRe.getId());
        rebindRecord.setBoundSide(BoundSide.CAPITAL);
        return rebindRecord;
    }

    private LvxinRebindRecord buildLvxinRebindRecord(BindCardRecord bindCardRecord, String orderNo) {
        LvxinRebindRecord rebindRecord = new LvxinRebindRecord();
        rebindRecord.setState(ProcessState.PROCESSING);
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(bindCardRecord.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecord.getId());
        rebindRecord.setBoundSide(bindCardRecord.getBoundSide());
        return rebindRecord;
    }

    /**
     * 计算剩余应还担保费总额
     *
     * @param loanId
     * @return
     */
    private BigDecimal calculateGuaranteeFee(String loanId) {
        List<RepayPlan> unpaidRepayPlanList = repayPlanRepository.findByLoanIdAndCustRepayState(loanId, RepayState.NORMAL);
        if (CollectionUtils.isEmpty(unpaidRepayPlanList)) {
            throw new LvxinBizException(LvxinResultCode.REPAY_PLAN_NORMAL_NOT_EXIST);
        }
        return unpaidRepayPlanList.stream()
            .map(e -> e.getGuaranteeAmt().subtract(AmountUtil.safeAmount(e.getActGuaranteeAmt())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private RepayPurpose getRepayPurpose(List<Integer> repayList, Loan loan) {
        RepayPurpose repayPurpose;
        // 当期还款时,只传一个期数 , 多个期数默认提前结清
        if (repayList.size() == 1) {
            repayPurpose = RepayPurpose.CURRENT;
            int repayPeriod = repayList.get(0);
            if (repayPeriod == loan.getPeriods()) {
                //最后一期,当前还款时间早于计划还款日,为提前结清
                RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), repayPeriod);
                if (LocalDate.now().isBefore(repayPlan.getPlanRepayDate())) {
                    repayPurpose = RepayPurpose.CLEAR;
                }
            }
        } else {
            repayPurpose = RepayPurpose.CLEAR;
        }
        return repayPurpose;
    }

    private void repayTrailCheck(Loan loan) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new LvxinBizException(LvxinResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE);
        }
    }

    public Integer toRepayPlanStatus(RepayPlan repayPlan) {

        RepayExtraGuaranteePlan extraFeePlan = repayExtraGuaranteePlanRepository.findByLoanIdAndPeriodAndPlanState(
            repayPlan.getLoanId(), repayPlan.getPeriod(), RepayState.NORMAL);
        //存在剩余未扣费用
        if (extraFeePlan != null) {
            return LvxinRepayStatus.PROCESSING.getCode();
        }

        switch (repayPlan.getCustRepayState()) {
            case NORMAL -> {
                //待还
                return LvxinRepayStatus.NORMAL.getCode();
            }
            case REPAID -> {
                return LvxinRepayStatus.CLEAR.getCode();
            }
            default -> {
                return null;
            }
        }
    }

    private static boolean checkOverdue(List<RepayPlan> repayPlanList) {
        //是否有逾期
        Optional<RepayPlan> isOverdue = repayPlanList.stream()
            .filter(item -> RepayState.NORMAL == item.getCustRepayState() && item.getPlanRepayDate().isBefore(LocalDate.now()))
            .findAny();
        return isOverdue.isPresent();
    }

    private static int getLvxinRepayStatus(List<RepayPlan> repayPlanList, Loan loan) {
        LvxinRepayStatus repayStatus = null;
        //已还期数
        long count = repayPlanList.stream().filter(repayPlan -> repayPlan.getCustRepayState() == RepayState.REPAID).count();
        if (count == loan.getPeriods()) {
            repayStatus = LvxinRepayStatus.CLEAR;
        } else if (count == 0) {
            repayStatus = LvxinRepayStatus.NORMAL;
        } else {
            repayStatus = LvxinRepayStatus.PROCESSING;
        }
        return repayStatus.getCode();
    }


    private static void checkParameters(LvxinLoanApplyRequest request) {
        if (StringUtil.isBlank(request.getPartnerUserId())) {
            throw new LvxinBizException("[partnerUserId]不能为空");
        }
        if (StringUtil.isBlank(request.getLoanGid())) {
            throw new LvxinBizException("[loanGid]不能为空");
        }
    }


    private static void checkParameters(LvxinGetRightsUrlRequest request) {
        if (StringUtil.isBlank(request.getPartnerUserId())) {
            throw new LvxinBizException("[partnerUserId]不能为空");
        }
        if (request.getScene() == null) {
            throw new LvxinBizException("[scene]不能为空");
        }
    }

    private static void checkParameters(LvxinRepayTrialRequest request) {
        String orderNo = request.getPartnerUserId();
        String loanId = request.getPartnerOrderNo();
        List<Integer> repayList = request.getRepayList();
        if (StringUtil.isBlank(orderNo)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_USERID_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(loanId)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_ORDERNO_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(request.getRepayType())) {
            throw new LvxinBizException(LvxinResultCode.REPAY_PURPOSE_ERROR);
        }
    }

    private static void checkParameters(LvxinRepayRequest request) {
        String orderNo = request.getPartnerUserId();
        String loanId = request.getPartnerOrderNo();

        if (StringUtil.isBlank(orderNo)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_USERID_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(loanId)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_ORDERNO_CAN_NOT_BE_NULL);
        }
        if (Objects.isNull(request.getPeriod()) || request.getPeriod() < 1) {
            throw new LvxinBizException(LvxinResultCode.PERIOD_ERROR);
        }
    }

    private static void checkParameters(LvxinRepayResultRequest request) {
        String orderNo = request.getPartnerOrderNo();
        String loanId = request.getLoanGid();
        String repaymentGid = request.getRepaymentGid();
        if (StringUtil.isBlank(orderNo)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_USERID_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(loanId)) {
            throw new LvxinBizException(LvxinResultCode.PARTNER_ORDERNO_CAN_NOT_BE_NULL);
        }
    }


    private Order platformCardSuccessProcess(BindCardRecord confirmResult, Order order) {
        order.setLoanCardId(confirmResult.getId());
        order = orderRepository.save(order);
        return order;
    }

    /**
     * 计算在贷余额
     *
     * @param userId 用户id
     * @return 在贷余额
     */
    private BigDecimal loanBalance(String userId, String riskId) {
        List<Order> orders = orderService.listByUserId(userId, FlowChannel.LVXIN);
        return orders.stream().filter(o -> riskId.equals(o.getRiskId()) && OrderState.LOAN_PASS == o.getOrderState())
            .map(Order::getApproveAmount)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Autowired
    FileService fileSer;

    private void checkFtpFileReady(CreditApplyRequest creditApplyRequest) {

        try {
            String bankChannel = SourceCodeToBankChannel(creditApplyRequest.getSourceCode());
            logger.info("资方渠道: {}", bankChannel);
            if ("CYBK".equals(bankChannel)) {
                logger.info("资方渠道{}，查看文件是否存在", bankChannel);
                sftpUtils.fileExists(getFtpUri(creditApplyRequest.getCreditApplyTime(), creditApplyRequest.getLoanReqNo(), creditApplyRequest.getIdNo()));
            } else if ("HXBK".equals(bankChannel)) {
                logger.info("资方渠道{}，查看文件是否存在", bankChannel);
                sftpUtils.fileExistsHx(getFtpUri(creditApplyRequest.getCreditApplyTime(), creditApplyRequest.getLoanReqNo(), creditApplyRequest.getIdNo()));
            }
            logger.info("资方渠道{}，查看检查影像信息文件完成", bankChannel);
        } catch (Exception e) {
            logger.error("与检查影像信息不存在", e);
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("检查证件信息不完整,请先上传证件信息");
            }
            throw new LvxinBizException("检查证件信息不完整");
        }
    }

    // 根据传入的sourceCode返回对应的BankChannel枚举值
    private String SourceCodeToBankChannel(String sourceCode) {
        switch (sourceCode) {
            case "CJCYDL_PPD2":
            case "CJCYDL_PPD3":
                return "CYBK";
            case "CJHBXJ_PPD2":
            case "CJHBXJ_PPD3":
                return "HXBK";
            // 如果sourceCode不匹配以上两种情况，则抛出异常
            default:
                throw new IllegalArgumentException("Unknown source code: " + sourceCode);
        }
    }

    private String[] getFtpUri(String creditApplyTime, String loanReqNo, String idNo) {
        LocalDate ymd = PpdConvert.toCreditApplyTimeLocalDateTime(creditApplyTime).toLocalDate();
        String formatYMD = DateTimeFormatter.ofPattern("yyyyMMdd").format(ymd);
        String baseDir = formatYMD + "/" + loanReqNo + "/";
        String idPositiveNation = baseDir + "id_" + idNo + "_01.jpg";
        String idNegativeHead = baseDir + "id_" + idNo + "_02.jpg";
        String livePhoto = baseDir + "id_" + idNo + "_03.jpg";
        String videoBefore = baseDir + "video_before.ok";
        String[] uris = new String[]{idPositiveNation, idNegativeHead, livePhoto, videoBefore};
        for (String uri : uris) {
            logger.info("Generated SFTP URI: " + uri);
        }
        return uris;
    }


    private UserOcr createUserOcr(PreOrder preOrder, PpdCreditApplyRecord applyRecord) {
        UserOcr userOcr = PpdConvert.INSTANCE.toUserOcr(applyRecord);
        userOcr.setHeadOssBucket(ossBucket);
        userOcr.setNationOssBucket(ossBucket);
        //身份证有效期
        userOcr.setCertValidStart(LocalDate.parse(applyRecord.getIdBeginDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        userOcr.setCertValidEnd(LocalDate.parse(applyRecord.getIdExpiryDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        return userOcr;
    }

    public void userFileDownloadAndUpload(String applyRecordId) {
        try {
            PpdCreditApplyRecord applyRecord = ppdCreditApplyRecordRepository.findById(applyRecordId).orElseThrow();
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(applyRecord.getLoanReqNo(), FlowChannel.PPCJDL).orElseThrow();
            String userId = preOrder.getOpenId();
            UserOcr userOcr = userOcrRepository.findByUserId(userId);
            UserFace userFace = userFaceRepository.findByUserId(userId);
            InputStream headImage = null;
            InputStream nationImage= null;
            InputStream faceImage= null;
            String[] ftpUri = getFtpUri(applyRecord.getCreditApplyTime(), applyRecord.getLoanReqNo(), applyRecord.getIdNo());

            String idPositiveNation = null;
            String idNegativeHead = null;
            String livePhoto = null;
            BankChannel bankChannel = preOrder.getBankChannel();
            if (bankChannel == BankChannel.CYBK) {
                idPositiveNation = ppdConfig.getSftpCreditApplyDownloadPath() + ftpUri[0];
                idNegativeHead = ppdConfig.getSftpCreditApplyDownloadPath() + ftpUri[1];
                livePhoto = ppdConfig.getSftpCreditApplyDownloadPath() + ftpUri[2];
                logger.info("idPositiveNation:{},idNegativeHead:{},faceImage:{}", idPositiveNation, idNegativeHead, livePhoto);
                headImage = sftpUtils.downloadAsStream(idPositiveNation);
                nationImage = sftpUtils.downloadAsStream(idNegativeHead);
                faceImage = sftpUtils.downloadAsStream(livePhoto);
            } else if (bankChannel == BankChannel.HXBK) {
                idPositiveNation = ppdConfig.getSftpHxCreditApplyDownloadPath() + ftpUri[0];
                idNegativeHead = ppdConfig.getSftpHxCreditApplyDownloadPath() + ftpUri[1];
                livePhoto = ppdConfig.getSftpHxCreditApplyDownloadPath() + ftpUri[2];
                logger.info("idPositiveNation:{},idNegativeHead:{},faceImage:{}", idPositiveNation, idNegativeHead, livePhoto);
                headImage = sftpUtils.downloadAsStreamHx(idPositiveNation);
                nationImage = sftpUtils.downloadAsStreamHx(idNegativeHead);
                faceImage = sftpUtils.downloadAsStreamHx(livePhoto);
            }
            idNegativeHead = StringUtils.substringBefore(idNegativeHead, "?");
            idPositiveNation = StringUtils.substringBefore(idPositiveNation, "?");
            livePhoto = StringUtils.substringBefore(livePhoto, "?");
            String headOssKey = uploadToOss(headImage, ossBucket, "head", applyRecord.getLoanReqNo(), StringUtils.substringAfterLast(idNegativeHead, "."));
            String nationOssKey = uploadToOss(nationImage, ossBucket, "nation", applyRecord.getLoanReqNo(), StringUtils.substringAfterLast(idPositiveNation, "."));
            String ossKey = uploadToOss(faceImage, ossBucket, "face", applyRecord.getLoanReqNo(), StringUtils.substringAfterLast(livePhoto, "."));
            userOcr.setHeadOssKey(headOssKey);
            userOcr.setNationOssKey(nationOssKey);
            userFace.setOssBucket(ossBucket);
            userFace.setOssKey(ossKey);
            userOcrRepository.save(userOcr);
            userFaceRepository.save(userFace);
            userFileService.saveIdCardFace(userId, userOcr.getHeadOssBucket(), userOcr.getHeadOssKey());
            userFileService.saveIdCardNation(userId, userOcr.getNationOssBucket(), userOcr.getNationOssKey());
            userFileService.saveFaceOcr(userId, userFace.getOssBucket(), userFace.getOssKey());
            // 内部风控
            UserRiskRecord riskRecord = userRiskRecordRepository.findTopByUserIdOrderByCreatedTimeDesc(userId);
            agreementService.applyRegisterSign(riskRecord.getId(),FlowChannel.PPCJDL,preOrder.getBankChannel() == null ? BankChannel.CYBK:preOrder.getBankChannel());
            mqService.submitRiskApply(riskRecord.getId());
        } catch (Exception e) {
            logger.error("下载拍拍贷影像件异常", e);
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("未在ftp中拉取到影像件文件,请先上传影像件信息");
            }
            throw new LvxinBizException("下载拍拍影像件异常");
        }
    }

    private UserFace createUserFace(PreOrder preOrder, PpdCreditApplyRecord applyRecord) {
        UserFace userFace = new UserFace();
        String faceSupplier = PpdConvert.toFaceSupplier(applyRecord.getFaceRecoChannel());
        userFace.setFaceChannel(faceSupplier);
        userFace.setFaceTime(LocalDateTime.now());
        userFace.setFaceScore(new BigDecimal(applyRecord.getFaceRecoScore()));
        userFace.setFacialSupplier(faceSupplier);

        return userFace;
    }

    private UserDevice createUserDevice(PpdCreditApplyRecord applyRecord) {
//        String deviceInfo = applyRecord.getDeviceInfo();
//        JSONObject jsonObject = JSONObject.parseObject(deviceInfo);
        UserDevice userDevice = new UserDevice();
//        userDevice.setOsType(jsonObject.getString("osType"));
//        userDevice.setOsVersion(jsonObject.getString("systemVersion"));
//        userDevice.setGps(applyRecord.getLongitude() + "," + applyRecord.getLatitude());
//        userDevice.setIp(jsonObject.getString("userIP"));
//        userDevice.setDeviceId(jsonObject.getString("deviceId"));
        return userDevice;
    }

    private List<UserContactInfo> createUserContactInfos(PpdCreditApplyRecord applyRecord) {
        List<ContractInfo> contractInfos = JSONObject.parseObject(applyRecord.getContactList(), new TypeReference<List<ContractInfo>>() {
        });
        return contractInfos.stream().map(item -> {
            UserContactInfo userContactInfo = new UserContactInfo();
            userContactInfo.setPhone(item.getContactMobile());
            userContactInfo.setName(item.getContactName());
            userContactInfo.setRelation(PpdConvert.INSTANCE.toPpdRelationsEnum(item.getContactRelation()));
            return userContactInfo;
        }).collect(Collectors.toList());
    }

    private String uploadToOss(InputStream inputStream, String bucket, String picType, String openId, String imageType) {
        String picKey = generateOssPicKey(openId, picType, imageType);
        try {
            fileService.uploadOss(bucket, picKey, inputStream);
        } catch (Exception e) {
            logger.error("lvxin info process pic error, openId: {}, fileType: {}", openId, picType);
            picKey = null;
        }
        return picKey;
    }

    private String generateOssPicKey(String openId, String prefix, String imageType) {
        String dayStr = DateUtil.formatShort(new Date());
        // fixme  定制化为lvxin/info/ 需要确认
        return "PPCJDL/info/" + dayStr + "/" + openId + "/" + prefix + "_"
            + UUID.randomUUID().toString().replaceAll("-", "") + "." + imageType;
    }

    //根据还款id查询》对客还款记录表
    public LvxinRepayResultResponse repayQuery(LvxinRepayResultRequest request) {
        checkParameters(request);
        LvxinRepayResultResponse lvxinRepayResult;

        LvxinRepayApplyRecord lvxinRepayApplyRecord = lvxinRepayApplyRecordRepository.findByOutRepayId(request.getRepaymentGid()).orElseThrow();

        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(lvxinRepayApplyRecord.getOutRepayId());
        Optional.ofNullable(customRepayRecord).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY));

        Optional<Loan> oLoan = loanRepository.findById(customRepayRecord.getLoanId());
        if (oLoan.isEmpty()) {
            throw new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY);
        }

        Order order = orderService.findById(oLoan.get().getOrderId());
        Optional.ofNullable(order).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY));

        // 查询还款计划
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(customRepayRecord.getLoanId(), customRepayRecord.getPeriod());
        if (Objects.isNull(repayPlan)) {
            throw new CommonApiBizException(CommonApiResultCode.REPAY_PLAN_NOT_EXIST);
        }

        if (repayPlan.getCustRepayState() == RepayState.REPAID) {
            repayPlan.setAmount(repayPlan.getActAmount());
            repayPlan.setGuaranteeAmt(repayPlan.getActGuaranteeAmt());
            repayPlan.setConsultFee(repayPlan.getActConsultFee());
            repayPlan.setPrincipalAmt(repayPlan.getActPrincipalAmt());
            repayPlan.setInterestAmt(repayPlan.getActInterestAmt());
            repayPlan.setPenaltyAmt(repayPlan.getActPenaltyAmt());
        }
        lvxinRepayResult = CommonApiCovert.INSTANCE.toRepayResultDTO(repayPlan);
        lvxinRepayResult.setActBreachAmt(repayPlan.getActBreachAmt());
        lvxinRepayResult.setPeriod(customRepayRecord.getPeriod());
        lvxinRepayResult.setActRepayTime(com.maguo.loan.cash.flow.util.DateUtil.formatLocalDateTime(customRepayRecord.getRepaidDate()));
        // 合作机构单号
        lvxinRepayResult.setPartnerOrderNo(order.getOuterOrderId());
        // 还款结果
        if (ProcessState.SUCCEED == customRepayRecord.getRepayState()) {
            lvxinRepayResult.setRepayStatus(RepayResult.REPAY_SUCCESS);
        } else if (ProcessState.FAILED == customRepayRecord.getRepayState()) {
            lvxinRepayResult.setRepayStatus(RepayResult.REPAY_FAIL);
        } else {
            lvxinRepayResult.setRepayStatus(RepayResult.REPAY_PROCESSING);
        }
        return lvxinRepayResult;
    }

    //文件影像上传接口
    public UploadResponse upload(UploadRequest quotaQueryRequest) {
        PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(quotaQueryRequest.getLoanOrderId(), FlowChannel.LVXIN)
            .orElseThrow(() -> new LvxinBizException(LvxinResultCode.PRE_ORDER_NOT_EXIST));
        LvxinApplyRecord applyRecord = lvxinApplyRecordRepository.findByOrderNo(quotaQueryRequest.getLoanOrderId()).orElse(null);
        List<UserFile> imageFiles = userFileRepository.findByUserIdAndFileTypeIn(preOrder.getOpenId(), List.of(quotaQueryRequest.getType()));

        UserOcr userOcr = LvxinConvert.INSTANCE.toUserOcr(applyRecord);
        //上传到oss
        //ToDo:需要上传影像文件
        InputStream headImage;
        String idPositive = quotaQueryRequest.getFileUrl();
        try {
            headImage = HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, idPositive);
        } catch (Exception e) {
            logger.error("下载绿信身份证正反面异常", e);
            throw new LvxinBizException("下载绿信身份证正反面异常");
        }
        idPositive = StringUtils.substringBefore(idPositive, "?");
        if (quotaQueryRequest.getType().name().equals("ID_HEAD")) {
            userOcr.setHeadOssBucket(ossBucket);
            String headOssKey = uploadToOss(headImage, ossBucket, "head", preOrder.getOpenId(), StringUtils.substringAfterLast(idPositive, "."));
            userOcr.setHeadOssKey(headOssKey);
            userFileService.saveIdCardFace(preOrder.getOpenId(), userOcr.getHeadOssBucket(), userOcr.getHeadOssKey());
        } else if (quotaQueryRequest.getType().name().equals("ID_NATION")) {
            userOcr.setNationOssBucket(ossBucket);
            String nationOssKey = uploadToOss(headImage, ossBucket, "nation", preOrder.getOpenId(), StringUtils.substringAfterLast(idPositive, "."));
            userOcr.setHeadOssKey(nationOssKey);
            userFileService.saveIdCardNation(preOrder.getOpenId(), userOcr.getNationOssBucket(), userOcr.getNationOssKey());
        } else if (quotaQueryRequest.getType().name().equals("ID_FACE")) {
            //
        }

        return null;
    }

    public LoanRevokeFundApplyResponse fundCancelApply(RepayPlanQueryRequest request) {
        LoanRevokeFundApplyResponse response = new LoanRevokeFundApplyResponse();
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(request.getLoanReqNo(), FlowChannel.PPCJDL);
        if (!(order.getOrderState() == OrderState.CREDIT_FAIL || order.getOrderState() == OrderState.LOAN_FAIL || order.getOrderState() == OrderState.LOAN_CANCEL)) {
            response.setStatus("01");
            response.setMsg("订单处理中拒绝撤销");
            return response;
        }

        PpdLoanApplyRecord byLoanReqNoAndSourceCode = ppdLoanApplyRecordRepository.findByLoanReqNoAndSourceCode(request.getLoanReqNo(), request.getSourceCode());

        if (StringUtils.equals(byLoanReqNoAndSourceCode.getRevoke(), "Y")) {
            response.setStatus("00");
            response.setMsg("订单已撤销");
            return response;
        }

        byLoanReqNoAndSourceCode.setRevoke("Y");
        ppdLoanApplyRecordRepository.save(byLoanReqNoAndSourceCode);
        response.setStatus("00");
        response.setMsg("撤销成功");
        return response;
    }

    public LoanRevokeFundApplyResponse fundCancelQuery(RepayPlanQueryRequest request) {
        LoanRevokeFundApplyResponse response = new LoanRevokeFundApplyResponse();
        PpdLoanApplyRecord byLoanReqNoAndSourceCode = ppdLoanApplyRecordRepository.findByLoanReqNoAndSourceCode(request.getLoanReqNo(), request.getSourceCode());
        if (StringUtils.equals(byLoanReqNoAndSourceCode.getRevoke(), "Y")) {
            response.setStatus("00");
            response.setMsg("撤销成功");
            return response;
        }

        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
        if (applyChannel == null) {
            throw new ValidationException("未知渠道号。", com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode.UNKNOWN_CHANNEL);
        }
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(request.getLoanReqNo(), FlowChannel.PPCJDL);
        if (!(order.getOrderState() == OrderState.CREDIT_FAIL || order.getOrderState() == OrderState.LOAN_FAIL || order.getOrderState() == OrderState.LOAN_CANCEL)) {
            response.setStatus("01");
            response.setMsg("订单处理中拒绝撤销");
            return response;
        }

        response.setStatus("99");
        response.setMsg("处理中");
        return response;
    }

}
