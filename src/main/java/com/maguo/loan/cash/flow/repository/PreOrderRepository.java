package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface PreOrderRepository extends JpaRepository<PreOrder, String> {

    Optional<PreOrder> findByOrderNoAndFlowChannel(String orderNo, FlowChannel flowChannel);

    Optional<PreOrder> findByOrderNoAndFlowChannelAndApplicationSource(String orderNo, FlowChannel flowChannel, ApplicationSource applicationSource);

    Boolean existsByCertNoAndFlowChannel(String certNo, FlowChannel flowChannel);

    Optional<PreOrder> findByRiskId(String riskId);

    List<PreOrder> findByCertNoAndFlowChannelOrderByCreatedTime(String certNo, FlowChannel flowChannel);

    PreOrder findTopByOpenIdOrderByCreatedTimeDesc(String userId);

    List<PreOrder> findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(String userId, PreOrderState preOrderState);

    List<PreOrder> findByApplyTimeBetweenAndPreOrderStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);

    PreOrder findTopByOpenIdAndFlowChannelOrderByCreatedTimeDesc(String userId,FlowChannel flowChannel);

    Optional<PreOrder> findByOpenIdAndFlowChannel(String userId, FlowChannel flowChannel);

    @Query("SELECT SUM(p.applyAmount) FROM PreOrder p WHERE p.flowChannel = :flowChannel AND p.bankChannel = :bankChannel AND p.preOrderState IN :preOrderState AND p.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByDimensionsAndStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("bankChannel") BankChannel bankChannel,
        @Param("preOrderState") PreOrderState preOrderState,
        @Param("startTime") LocalDateTime todayStart,
        @Param("endTime") LocalDateTime todayEnd);


    @Query("SELECT SUM(p.applyAmount) FROM PreOrder p WHERE p.flowChannel = :flowChannel AND p.preOrderState IN :preOrderState AND p.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("preOrderState") PreOrderState preOrderState,
        @Param("startTime") LocalDateTime todayStart,
        @Param("endTime") LocalDateTime todayEnd);

    @Query(nativeQuery = true, value = """
            SELECT
                COALESCE((select sum(credit_amt) from credit where state = 'SUCCEED' and user_id = :userId), 0) AS transLimitCur,
                COALESCE((SELECT SUM(r.act_principal_amt) AS act_principal_amt FROM `order` o INNER JOIN loan l ON l.order_id = o.id
                                INNER JOIN repay_plan r ON r.loan_id = l.id WHERE o.user_id = :userId and o.order_state != 'CLEAR'), 0) AS transLimitUsed,
                COALESCE(( SELECT c.credit_amt FROM `credit` c WHERE c.user_id = :userId AND c.flow_channel = :flowChannel and state = 'SUCCEED' ORDER BY c.apply_time LIMIT 1), 0) AS initial_amount,
                COALESCE((SELECT COUNT(1) FROM credit c WHERE c.user_id = :userId AND c.flow_channel = :flowChannel), 0) AS his_credit_appl_num,
                COALESCE((SELECT COUNT(1) FROM credit c WHERE c.user_id = :userId AND c.flow_channel = :flowChannel and state = 'SUCCEED'), 0) AS his_credit_appl_suc_num,
                COALESCE((SELECT DATEDIFF(CURDATE(), c.apply_time) FROM credit c WHERE c.user_id = :userId AND c.flow_channel = :flowChannel ORDER BY c.apply_time DESC LIMIT 1), 0) AS credit_appl_days,
                COALESCE((SELECT COUNT(1) FROM loan WHERE user_id = :userId  AND flow_channel = :flowChannel AND loan_state = 'SUCCEED'), 0) AS loan_hit_apl_num,
                COALESCE((SELECT COUNT(1) FROM loan WHERE user_id = :userId AND flow_channel = :flowChannel), 0) AS loan_hit_apl_suc_num,
                COALESCE((SELECT DATEDIFF(CURDATE(), apply_time) FROM loan WHERE user_id = :userId AND flow_channel = :flowChannel ORDER BY apply_time DESC LIMIT 1), 0) AS loan_lst_apl_days,
                COALESCE((SELECT COUNT(1) FROM repay_plan  WHERE user_id = :userId AND ((cust_repay_state = 'REPAID'
                AND act_repay_time > plan_repay_date) OR (cust_repay_state = 'NORMAL' AND CURDATE() > plan_repay_date))), 0) AS reapy_his_ovd_num,
                COALESCE((SELECT MAX(principal_amt) FROM repay_plan WHERE user_id = :userId AND cust_repay_state = 'REPAID' AND act_repay_time > plan_repay_date), 0) AS reapy_his_ovd_amt_max,
                COALESCE((SELECT MAX(principal_amt) FROM repay_plan WHERE user_id = :userId AND cust_repay_state = 'NORMAL' AND CURDATE() > plan_repay_date), 0) AS reapy_cur_ovd_amt_max,
                COALESCE((SELECT SUM(principal_amt) FROM repay_plan WHERE user_id = :userId AND cust_repay_state = 'NORMAL' AND CURDATE() > plan_repay_date), 0) AS reapy_cur_ovd_amt_sum,
                COALESCE((SELECT SUM(principal_amt) FROM repay_plan WHERE user_id = :userId AND cust_repay_state = 'REPAID' ), 0) AS repay_tot_repaid_amt,
                COALESCE((SELECT SUM(cust_repay_state = 'REPAID' AND DATE(act_repay_time) <= plan_repay_date) / COUNT(1) FROM repay_plan  WHERE user_id = :userId), 0) AS repay_nml_phase_pct_term
            """)
    Map<String, Object> findUserApplyAmountHistory(@Param("userId") String userId,@Param("flowChannel") String flowChannel);

}
