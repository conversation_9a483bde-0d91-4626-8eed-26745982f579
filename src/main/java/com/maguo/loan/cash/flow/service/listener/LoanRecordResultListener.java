package com.maguo.loan.cash.flow.service.listener;



import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 放款结果查询
 */
@Component
public class LoanRecordResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(LoanRecordResultListener.class);
    public LoanRecordResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private LoanService loanService;

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_RECORD_QUERY)
    public void loanRecordResultListen(Message message, Channel channel) {
        String loanRecordId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听放款查询core:{}", loanRecordId);
            // service
            loanService.bankLoanRecordResult(loanRecordId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            processException(loanRecordId, message, e, "放款查询core异常", getMqService()::submitLoanRecordResultQueryDelay);
        } finally {
            ackMsg(loanRecordId, message, channel);
        }
    }
}
