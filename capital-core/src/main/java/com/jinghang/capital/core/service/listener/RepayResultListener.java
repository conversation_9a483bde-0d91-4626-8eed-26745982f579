package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class RepayResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayResultListener.class);

    public RepayResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_QUERY)
    public void listenRepayResult(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听还款结果查询:{}", repayId);
            // manageService
            //todo 还款结果回调测试，长银侧被主动查询到结果后不会再回调
//            manageService.repayQuery(repayId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.error("还款异常，bankRepayId: {}", repayId, e);
            processException(repayId, message, e, "查询还款结果异常", getMqService()::submitRepayQueryDelay);
        } finally {
            ackMsg(repayId, message, channel);
        }
    }
}
